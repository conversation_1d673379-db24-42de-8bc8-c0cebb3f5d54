import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function ProblemCategorySelection({ token }) {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const ticketNumber = searchParams.get("ticket");
  const accessToken = token || localStorage.getItem("access");
  
  const [categories, setCategories] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/problem_categories/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const data = await response.json();
      if (response.ok) {
        setCategories(data.categories);
      } else {
        setError("Failed to load problem categories");
      }
    } catch (err) {
      setError("Network error: " + err.message);
    }
  };

  const handleCategoryToggle = (categoryId) => {
    setSelectedCategories(prev => {
      if (prev.includes(categoryId)) {
        return prev.filter(id => id !== categoryId);
      } else {
        return [...prev, categoryId];
      }
    });
  };

  const handleSubmit = async () => {
    if (selectedCategories.length === 0) {
      setError("Please select at least one problem category");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`${BACKEND_URL}/api/save_problem_categories/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          ticket_number: ticketNumber,
          category_ids: selectedCategories,
        }),
      });

      const data = await response.json();
      if (response.ok) {
        // Redirect to chatbot
        navigate(`/chatbot/${ticketNumber}`);
      } else {
        setError(data.error || "Failed to save problem categories");
      }
    } catch (err) {
      setError("Network error: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  if (!ticketNumber) {
    return (
      <div style={{ padding: "40px", textAlign: "center" }}>
        <h2 style={{ color: "#d32f2f" }}>Error</h2>
        <p>No ticket number provided</p>
        <button onClick={() => navigate("/actions")} style={{ marginTop: "20px", padding: "10px 20px" }}>
          Back to Actions
        </button>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "600px", 
      margin: "0 auto", 
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        textAlign: "center"
      }}>
        Problem Category Selection
      </h1>
      
      <p style={{
        fontSize: "1.1rem",
        color: "#666",
        marginBottom: "30px",
        textAlign: "center"
      }}>
        To help us assist you better, please select one or more problem categories that apply to your issue:
      </p>

      <div style={{
        backgroundColor: "#f8f9fa",
        padding: "20px",
        borderRadius: "8px",
        marginBottom: "20px",
        border: "1px solid #e9ecef"
      }}>
        <p style={{ margin: "0 0 10px 0", fontWeight: "bold", color: "#333" }}>
          Ticket: {ticketNumber}
        </p>
      </div>

      {error && (
        <div style={{
          backgroundColor: "#ffebee",
          color: "#c62828",
          padding: "12px",
          borderRadius: "4px",
          marginBottom: "20px",
          border: "1px solid #ef5350"
        }}>
          {error}
        </div>
      )}

      <div style={{ marginBottom: "30px" }}>
        {categories.map((category) => (
          <div
            key={category.id}
            style={{
              display: "flex",
              alignItems: "flex-start",
              marginBottom: "15px",
              padding: "15px",
              border: selectedCategories.includes(category.id) 
                ? "2px solid #2196f3" 
                : "1px solid #ddd",
              borderRadius: "8px",
              backgroundColor: selectedCategories.includes(category.id) 
                ? "#e3f2fd" 
                : "white",
              cursor: "pointer",
              transition: "all 0.2s ease"
            }}
            onClick={() => handleCategoryToggle(category.id)}
          >
            <input
              type="checkbox"
              checked={selectedCategories.includes(category.id)}
              onChange={() => handleCategoryToggle(category.id)}
              style={{
                marginRight: "12px",
                marginTop: "2px",
                transform: "scale(1.2)"
              }}
            />
            <div>
              <div style={{
                fontWeight: "bold",
                color: "#333",
                marginBottom: "5px"
              }}>
                {category.name}
              </div>
              {category.description && (
                <div style={{
                  color: "#666",
                  fontSize: "14px"
                }}>
                  {category.description}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      <div style={{ 
        display: "flex", 
        gap: "15px", 
        justifyContent: "center",
        marginTop: "30px"
      }}>
        <button
          onClick={() => navigate("/actions")}
          style={{
            padding: "12px 24px",
            backgroundColor: "#6c757d",
            color: "white",
            border: "none",
            borderRadius: "6px",
            fontSize: "16px",
            cursor: "pointer",
            fontWeight: "500"
          }}
        >
          Back
        </button>
        
        <button
          onClick={handleSubmit}
          disabled={loading || selectedCategories.length === 0}
          style={{
            padding: "12px 24px",
            backgroundColor: selectedCategories.length > 0 ? "#2196f3" : "#ccc",
            color: "white",
            border: "none",
            borderRadius: "6px",
            fontSize: "16px",
            cursor: selectedCategories.length > 0 ? "pointer" : "not-allowed",
            fontWeight: "500"
          }}
        >
          {loading ? "Saving..." : "Continue to Chat"}
        </button>
      </div>
    </div>
  );
}
