"""
Enhanced RAG System for Camera Manuals with Weaviate
Supports multiple camera types and models with advanced metadata extraction
"""

import os
import re
import json
import uuid
import hashlib
import weaviate
import openai
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import PyPDF2
from io import BytesIO
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
WEAVIATE_URL = "http://localhost:8080"
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "your-openai-api-key")
EMBEDDING_MODEL = "text-embedding-ada-002"
CAMERA_MANUAL_CLASS = "CameraManual"

# Initialize clients
openai.api_key = OPENAI_API_KEY
weaviate_client = weaviate.Client(url=WEAVIATE_URL)

@dataclass
class CameraManualChunk:
    """Data structure for camera manual chunks"""
    source_file: str
    chunk_number: int
    content: str
    camera_type: str
    model_names: List[str]
    section_type: Optional[str] = None
    embedding: Optional[List[float]] = None

class CameraTypeExtractor:
    """Extract camera type from filename"""
    
    CAMERA_TYPE_PATTERNS = {
        r'nano[_\-]?5g': 'Nano-5G',
        r'nano[_\-]?10g': 'Nano-10G',
        r'genie[_\-]?nano': 'Genie-Nano',
        r'linea[_\-]?hs': 'Linea-HS',
        r'linea[_\-]?ml': 'Linea-ML',
        r'spyder[_\-]?3': 'Spyder-3',
        r'ace[_\-]?2': 'Ace-2',
        r'dart[_\-]?e': 'Dart-E',
        r'blackfly[_\-]?s': 'Blackfly-S',
    }
    
    @classmethod
    def extract_camera_type(cls, filename: str) -> str:
        """Extract camera type from filename"""
        filename_lower = filename.lower()
        
        for pattern, camera_type in cls.CAMERA_TYPE_PATTERNS.items():
            if re.search(pattern, filename_lower):
                return camera_type
        
        # Fallback: extract from common patterns
        if 'nano' in filename_lower:
            return 'Nano-Series'
        elif 'linea' in filename_lower:
            return 'Linea-Series'
        elif 'genie' in filename_lower:
            return 'Genie-Series'
        
        return 'Unknown'

class ModelNameExtractor:
    """Extract model names from content"""
    
    MODEL_PATTERNS = [
        r'\b[A-Z]\d{4}[A-Z]?(?:-\d+[A-Z]?)?\b',  # M2050, C2450, M2050-25G
        r'\b[A-Z]{2}\d{4}[A-Z]?(?:-\d+[A-Z]?)?\b',  # GS2450, ML4096
        r'\bGC\d{4}[A-Z]?(?:-\d+[A-Z]?)?\b',  # GC2450-C
        r'\bBFS-[A-Z0-9\-]+\b',  # BFS-U3-31S4M-C
        r'\bDFK\s?\d{2}[A-Z]\d{3}\b',  # DFK 23G618
    ]
    
    @classmethod
    def extract_model_names(cls, content: str) -> List[str]:
        """Extract model names from content"""
        models = set()
        
        for pattern in cls.MODEL_PATTERNS:
            matches = re.findall(pattern, content, re.IGNORECASE)
            models.update(matches)
        
        # Clean and validate models
        valid_models = []
        for model in models:
            model = model.strip().upper()
            if len(model) >= 4 and model not in valid_models:
                valid_models.append(model)
        
        return valid_models[:10]  # Limit to 10 models per chunk

class SectionBasedChunker:
    """Advanced chunking based on document sections"""
    
    SECTION_PATTERNS = {
        'overview': [
            r'overview', r'introduction', r'about', r'general\s+information',
            r'product\s+description', r'features'
        ],
        'specifications': [
            r'specifications?', r'technical\s+specifications?', r'specs',
            r'performance', r'characteristics', r'parameters'
        ],
        'installation': [
            r'installation', r'setup', r'mounting', r'hardware\s+installation',
            r'physical\s+installation'
        ],
        'configuration': [
            r'configuration', r'settings', r'parameters', r'camera\s+settings',
            r'software\s+configuration'
        ],
        'firmware': [
            r'firmware', r'software', r'drivers?', r'sdk', r'api',
            r'programming', r'development'
        ],
        'troubleshooting': [
            r'troubleshooting', r'problems?', r'issues?', r'faq',
            r'common\s+problems', r'error', r'diagnostic'
        ],
        'maintenance': [
            r'maintenance', r'cleaning', r'care', r'service',
            r'preventive\s+maintenance'
        ]
    }
    
    @classmethod
    def identify_section(cls, content: str) -> str:
        """Identify section type from content"""
        content_lower = content.lower()
        
        for section_type, patterns in cls.SECTION_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, content_lower):
                    return section_type
        
        return 'general'
    
    @classmethod
    def chunk_by_sections(cls, text: str, max_chunk_size: int = 1000) -> List[Dict]:
        """Chunk text by logical sections"""
        # Split by common section delimiters
        section_delimiters = [
            r'\n\s*\d+\.?\s+[A-Z][^.\n]*\n',  # Numbered sections
            r'\n\s*[A-Z][A-Z\s]{10,}\n',      # ALL CAPS headers
            r'\n\s*#{1,6}\s+[^\n]+\n',        # Markdown headers
            r'\n\s*[A-Z][^.\n]{20,}\n\s*[-=]{3,}\n',  # Underlined headers
        ]
        
        chunks = []
        current_chunk = ""
        current_section = "general"
        
        # Split text into paragraphs
        paragraphs = re.split(r'\n\s*\n', text)
        
        for para in paragraphs:
            para = para.strip()
            if not para:
                continue
            
            # Check if this paragraph is a section header
            section_type = cls.identify_section(para)
            if section_type != 'general' and len(para) < 200:
                # This looks like a section header
                if current_chunk:
                    chunks.append({
                        'content': current_chunk.strip(),
                        'section_type': current_section
                    })
                current_chunk = para
                current_section = section_type
            else:
                # Add to current chunk
                if len(current_chunk + "\n\n" + para) <= max_chunk_size:
                    current_chunk += "\n\n" + para if current_chunk else para
                else:
                    # Save current chunk and start new one
                    if current_chunk:
                        chunks.append({
                            'content': current_chunk.strip(),
                            'section_type': current_section
                        })
                    current_chunk = para
        
        # Add final chunk
        if current_chunk:
            chunks.append({
                'content': current_chunk.strip(),
                'section_type': current_section
            })
        
        return chunks

class WeaviateSchemaManager:
    """Manage Weaviate schema for camera manuals"""
    
    @staticmethod
    def create_camera_manual_schema() -> bool:
        """Create CameraManual class in Weaviate"""
        try:
            # Check if class already exists
            if weaviate_client.schema.contains({"class": CAMERA_MANUAL_CLASS}):
                logger.info(f"✅ Class {CAMERA_MANUAL_CLASS} already exists")
                return True
            
            # Define schema
            schema = {
                "class": CAMERA_MANUAL_CLASS,
                "description": "Camera manual chunks with metadata",
                "vectorizer": "none",  # External embeddings
                "properties": [
                    {
                        "name": "source_file",
                        "dataType": ["text"],
                        "description": "Filename of the manual"
                    },
                    {
                        "name": "chunk_number",
                        "dataType": ["int"],
                        "description": "Sequential chunk ID"
                    },
                    {
                        "name": "content",
                        "dataType": ["text"],
                        "description": "Actual chunked text from the manual"
                    },
                    {
                        "name": "camera_type",
                        "dataType": ["text"],
                        "description": "Camera type extracted from filename"
                    },
                    {
                        "name": "model_name",
                        "dataType": ["text[]"],
                        "description": "One or more models mentioned in the chunk"
                    },
                    {
                        "name": "section_type",
                        "dataType": ["text"],
                        "description": "Type of section (overview, specifications, etc.)"
                    },
                    {
                        "name": "file_hash",
                        "dataType": ["text"],
                        "description": "Hash of source file for deduplication"
                    },
                    {
                        "name": "created_at",
                        "dataType": ["date"],
                        "description": "When the chunk was created"
                    }
                ]
            }
            
            # Create class
            weaviate_client.schema.create_class(schema)
            logger.info(f"✅ Created Weaviate class: {CAMERA_MANUAL_CLASS}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error creating schema: {e}")
            return False
    
    @staticmethod
    def delete_camera_manual_schema() -> bool:
        """Delete CameraManual class (for testing)"""
        try:
            weaviate_client.schema.delete_class(CAMERA_MANUAL_CLASS)
            logger.info(f"🗑️ Deleted class: {CAMERA_MANUAL_CLASS}")
            return True
        except Exception as e:
            logger.error(f"❌ Error deleting schema: {e}")
            return False

class EmbeddingGenerator:
    """Generate embeddings using OpenAI"""

    @staticmethod
    def get_embedding(text: str) -> List[float]:
        """Generate embedding for text"""
        try:
            response = openai.Embedding.create(
                input=text,
                model=EMBEDDING_MODEL
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"❌ Error generating embedding: {e}")
            return []

    @staticmethod
    def batch_embeddings(texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        embeddings = []
        batch_size = 100  # OpenAI limit

        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            try:
                response = openai.Embedding.create(
                    input=batch,
                    model=EMBEDDING_MODEL
                )
                batch_embeddings = [item.embedding for item in response.data]
                embeddings.extend(batch_embeddings)
            except Exception as e:
                logger.error(f"❌ Error in batch embedding: {e}")
                # Add empty embeddings for failed batch
                embeddings.extend([[] for _ in batch])

        return embeddings

class CameraManualProcessor:
    """Main processor for camera manuals"""

    def __init__(self):
        self.camera_extractor = CameraTypeExtractor()
        self.model_extractor = ModelNameExtractor()
        self.chunker = SectionBasedChunker()
        self.embedding_generator = EmbeddingGenerator()

    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            logger.error(f"❌ Error extracting PDF text: {e}")
            return ""

    def extract_text_from_bytes(self, file_data: bytes) -> str:
        """Extract text from PDF bytes"""
        try:
            pdf_reader = PyPDF2.PdfReader(BytesIO(file_data))
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            return text
        except Exception as e:
            logger.error(f"❌ Error extracting PDF text from bytes: {e}")
            return ""

    def calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of file"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.sha256(f.read()).hexdigest()
        except Exception as e:
            logger.error(f"❌ Error calculating file hash: {e}")
            return ""

    def process_manual(self, file_path: str, max_chunk_size: int = 1000) -> List[CameraManualChunk]:
        """Process a camera manual into chunks"""
        logger.info(f"📄 Processing manual: {file_path}")

        # Extract metadata
        filename = Path(file_path).name
        camera_type = self.camera_extractor.extract_camera_type(filename)
        file_hash = self.calculate_file_hash(file_path)

        # Extract text
        text = self.extract_text_from_pdf(file_path)
        if not text:
            logger.error(f"❌ No text extracted from {filename}")
            return []

        # Chunk text
        raw_chunks = self.chunker.chunk_by_sections(text, max_chunk_size)

        # Process chunks
        chunks = []
        for idx, raw_chunk in enumerate(raw_chunks):
            content = raw_chunk['content']
            section_type = raw_chunk['section_type']

            # Extract model names
            model_names = self.model_extractor.extract_model_names(content)

            # Create chunk object
            chunk = CameraManualChunk(
                source_file=filename,
                chunk_number=idx + 1,
                content=content,
                camera_type=camera_type,
                model_names=model_names,
                section_type=section_type
            )
            chunks.append(chunk)

        logger.info(f"✅ Created {len(chunks)} chunks for {filename}")
        return chunks

    def store_chunks_in_weaviate(self, chunks: List[CameraManualChunk]) -> int:
        """Store chunks in Weaviate with embeddings"""
        if not chunks:
            return 0

        logger.info(f"🔄 Storing {len(chunks)} chunks in Weaviate...")

        # Generate embeddings in batch
        texts = [chunk.content for chunk in chunks]
        embeddings = self.embedding_generator.batch_embeddings(texts)

        stored_count = 0
        for chunk, embedding in zip(chunks, embeddings):
            if not embedding:  # Skip if embedding failed
                continue

            try:
                # Prepare data object
                data_object = {
                    "source_file": chunk.source_file,
                    "chunk_number": chunk.chunk_number,
                    "content": chunk.content,
                    "camera_type": chunk.camera_type,
                    "model_name": chunk.model_names,
                    "section_type": chunk.section_type,
                    "file_hash": self.calculate_file_hash(chunk.source_file) if os.path.exists(chunk.source_file) else "",
                    "created_at": "2024-01-01T00:00:00Z"  # Current timestamp
                }

                # Store in Weaviate
                weaviate_client.data_object.create(
                    data_object=data_object,
                    class_name=CAMERA_MANUAL_CLASS,
                    vector=embedding
                )

                stored_count += 1
                logger.info(f"✅ Stored chunk {chunk.chunk_number} from {chunk.source_file}")

            except Exception as e:
                logger.error(f"❌ Error storing chunk {chunk.chunk_number}: {e}")
                continue

        logger.info(f"✅ Successfully stored {stored_count}/{len(chunks)} chunks")
        return stored_count

class CameraManualQueryEngine:
    """Advanced query engine for camera manuals"""

    def __init__(self):
        self.embedding_generator = EmbeddingGenerator()

    def search_manuals(self,
                      query: str,
                      camera_type: Optional[str] = None,
                      model_name: Optional[str] = None,
                      section_type: Optional[str] = None,
                      limit: int = 5,
                      certainty_threshold: float = 0.7) -> List[Dict]:
        """
        Search camera manuals with advanced filtering

        Args:
            query: Search query text
            camera_type: Filter by camera type (e.g., "Nano-5G")
            model_name: Filter by specific model (e.g., "M2050")
            section_type: Filter by section type (e.g., "specifications")
            limit: Maximum number of results
            certainty_threshold: Minimum certainty score

        Returns:
            List of matching chunks with metadata
        """
        try:
            logger.info(f"🔍 Searching for: '{query[:100]}...'")

            # Generate query embedding
            query_embedding = self.embedding_generator.get_embedding(query)
            if not query_embedding:
                logger.error("❌ Failed to generate query embedding")
                return []

            # Build query
            query_builder = (
                weaviate_client.query
                .get(CAMERA_MANUAL_CLASS, [
                    "source_file", "chunk_number", "content", "camera_type",
                    "model_name", "section_type"
                ])
                .with_near_vector({
                    "vector": query_embedding,
                    "certainty": certainty_threshold
                })
                .with_additional(["certainty", "distance"])
                .with_limit(limit)
            )

            # Add filters
            where_conditions = []

            if camera_type:
                where_conditions.append({
                    "path": ["camera_type"],
                    "operator": "Equal",
                    "valueText": camera_type
                })
                logger.info(f"🔍 Filtering by camera_type: {camera_type}")

            if model_name:
                where_conditions.append({
                    "path": ["model_name"],
                    "operator": "ContainsAny",
                    "valueTextArray": [model_name]
                })
                logger.info(f"🔍 Filtering by model_name: {model_name}")

            if section_type:
                where_conditions.append({
                    "path": ["section_type"],
                    "operator": "Equal",
                    "valueText": section_type
                })
                logger.info(f"🔍 Filtering by section_type: {section_type}")

            # Apply filters
            if where_conditions:
                if len(where_conditions) == 1:
                    query_builder = query_builder.with_where(where_conditions[0])
                else:
                    query_builder = query_builder.with_where({
                        "operator": "And",
                        "operands": where_conditions
                    })

            # Execute query
            response = query_builder.do()

            # Process results
            results = response.get("data", {}).get("Get", {}).get(CAMERA_MANUAL_CLASS, [])

            logger.info(f"✅ Found {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"❌ Error searching manuals: {e}")
            return []

    def search_by_camera_series(self, query: str, camera_type: str, limit: int = 10) -> List[Dict]:
        """Search all models within a camera series"""
        return self.search_manuals(
            query=query,
            camera_type=camera_type,
            limit=limit
        )

    def search_specific_model(self, query: str, model_name: str, limit: int = 5) -> List[Dict]:
        """Search for specific model information"""
        return self.search_manuals(
            query=query,
            model_name=model_name,
            limit=limit
        )

    def get_model_specifications(self, model_name: str) -> List[Dict]:
        """Get specifications for a specific model"""
        return self.search_manuals(
            query="specifications technical parameters performance",
            model_name=model_name,
            section_type="specifications",
            limit=3
        )

    def get_troubleshooting_info(self, model_name: str, issue: str) -> List[Dict]:
        """Get troubleshooting information for a model"""
        query = f"troubleshooting problem issue error {issue}"
        return self.search_manuals(
            query=query,
            model_name=model_name,
            section_type="troubleshooting",
            limit=5
        )

class CameraRAGSystem:
    """Complete RAG system for camera manuals"""

    def __init__(self):
        self.schema_manager = WeaviateSchemaManager()
        self.processor = CameraManualProcessor()
        self.query_engine = CameraManualQueryEngine()

    def initialize_system(self) -> bool:
        """Initialize the RAG system"""
        logger.info("🚀 Initializing Camera RAG System...")

        # Create schema
        if not self.schema_manager.create_camera_manual_schema():
            logger.error("❌ Failed to create schema")
            return False

        logger.info("✅ Camera RAG System initialized successfully")
        return True

    def process_manual_file(self, file_path: str) -> bool:
        """Process and store a single manual file"""
        try:
            # Process manual
            chunks = self.processor.process_manual(file_path)
            if not chunks:
                logger.error(f"❌ No chunks created for {file_path}")
                return False

            # Store in Weaviate
            stored_count = self.processor.store_chunks_in_weaviate(chunks)

            logger.info(f"✅ Processed {file_path}: {stored_count} chunks stored")
            return stored_count > 0

        except Exception as e:
            logger.error(f"❌ Error processing {file_path}: {e}")
            return False

    def process_manual_directory(self, directory_path: str) -> Dict[str, int]:
        """Process all PDF files in a directory"""
        directory = Path(directory_path)
        results = {}

        if not directory.exists():
            logger.error(f"❌ Directory not found: {directory_path}")
            return results

        pdf_files = list(directory.glob("*.pdf"))
        logger.info(f"📁 Found {len(pdf_files)} PDF files in {directory_path}")

        for pdf_file in pdf_files:
            logger.info(f"📄 Processing: {pdf_file.name}")
            chunks = self.processor.process_manual(str(pdf_file))
            stored_count = self.processor.store_chunks_in_weaviate(chunks)
            results[pdf_file.name] = stored_count

        total_stored = sum(results.values())
        logger.info(f"✅ Processed {len(pdf_files)} files, stored {total_stored} total chunks")
        return results

    def query_manuals(self,
                     query: str,
                     camera_type: Optional[str] = None,
                     model_name: Optional[str] = None,
                     top_k: int = 5) -> Dict[str, Any]:
        """
        Query the camera manual system and generate response

        Args:
            query: User query
            camera_type: Optional camera type filter
            model_name: Optional model name filter
            top_k: Number of chunks to retrieve

        Returns:
            Dictionary with chunks, answer, and metadata
        """
        try:
            # Search for relevant chunks
            chunks = self.query_engine.search_manuals(
                query=query,
                camera_type=camera_type,
                model_name=model_name,
                limit=top_k
            )

            if not chunks:
                return {
                    "chunks": [],
                    "answer": "No relevant information found in the camera manuals.",
                    "source_files": [],
                    "models_found": []
                }

            # Extract metadata
            source_files = list(set(chunk.get("source_file", "") for chunk in chunks))
            models_found = []
            for chunk in chunks:
                models = chunk.get("model_name", [])
                if isinstance(models, list):
                    models_found.extend(models)
                else:
                    models_found.append(models)
            models_found = list(set(models_found))

            # Prepare context for LLM
            context_text = "\n\n".join([
                f"Source: {chunk.get('source_file', 'Unknown')}\n"
                f"Section: {chunk.get('section_type', 'general')}\n"
                f"Models: {', '.join(chunk.get('model_name', []))}\n"
                f"Content: {chunk.get('content', '')}"
                for chunk in chunks
            ])

            # Generate answer using GPT
            answer = self.generate_answer(query, context_text)

            return {
                "chunks": chunks,
                "answer": answer,
                "source_files": source_files,
                "models_found": models_found,
                "context_length": len(context_text)
            }

        except Exception as e:
            logger.error(f"❌ Error querying manuals: {e}")
            return {
                "chunks": [],
                "answer": f"Error processing query: {str(e)}",
                "source_files": [],
                "models_found": []
            }

    def generate_answer(self, query: str, context: str) -> str:
        """Generate answer using GPT with retrieved context"""
        try:
            prompt = f"""You are a technical support assistant for camera systems. Use the provided context from camera manuals to answer the user's question accurately and comprehensively.

Context from Camera Manuals:
{context}

User Question: {query}

Instructions:
1. Answer based primarily on the provided context
2. Be specific about which camera models the information applies to
3. Include relevant technical specifications when available
4. If the context doesn't contain enough information, clearly state this
5. Provide step-by-step instructions when appropriate
6. Reference the source manual when possible

Answer:"""

            response = openai.ChatCompletion.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a technical support assistant specializing in camera systems and manuals."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.1
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"❌ Error generating answer: {e}")
            return "I apologize, but I encountered an error while generating the response. Please try again."

# Management and utility functions
class CameraRAGManager:
    """Management utilities for the Camera RAG system"""

    def __init__(self):
        self.rag_system = CameraRAGSystem()

    def get_system_stats(self) -> Dict[str, Any]:
        """Get system statistics"""
        try:
            # Query total chunks
            response = weaviate_client.query.aggregate(CAMERA_MANUAL_CLASS).with_meta_count().do()
            total_chunks = response.get("data", {}).get("Aggregate", {}).get(CAMERA_MANUAL_CLASS, [{}])[0].get("meta", {}).get("count", 0)

            # Get unique camera types
            camera_types_response = weaviate_client.query.get(CAMERA_MANUAL_CLASS, ["camera_type"]).with_limit(1000).do()
            camera_types = set()
            if camera_types_response.get("data", {}).get("Get", {}).get(CAMERA_MANUAL_CLASS):
                for item in camera_types_response["data"]["Get"][CAMERA_MANUAL_CLASS]:
                    camera_types.add(item.get("camera_type", ""))

            # Get unique source files
            files_response = weaviate_client.query.get(CAMERA_MANUAL_CLASS, ["source_file"]).with_limit(1000).do()
            source_files = set()
            if files_response.get("data", {}).get("Get", {}).get(CAMERA_MANUAL_CLASS):
                for item in files_response["data"]["Get"][CAMERA_MANUAL_CLASS]:
                    source_files.add(item.get("source_file", ""))

            return {
                "total_chunks": total_chunks,
                "unique_camera_types": len(camera_types),
                "camera_types": list(camera_types),
                "unique_source_files": len(source_files),
                "source_files": list(source_files)
            }

        except Exception as e:
            logger.error(f"❌ Error getting system stats: {e}")
            return {"error": str(e)}

    def delete_manual_data(self, source_file: str) -> bool:
        """Delete all chunks for a specific manual"""
        try:
            weaviate_client.batch.delete_objects(
                class_name=CAMERA_MANUAL_CLASS,
                where={
                    "path": ["source_file"],
                    "operator": "Equal",
                    "valueText": source_file
                }
            )
            logger.info(f"🗑️ Deleted data for: {source_file}")
            return True
        except Exception as e:
            logger.error(f"❌ Error deleting manual data: {e}")
            return False

    def reset_system(self) -> bool:
        """Reset the entire system (delete all data)"""
        try:
            # Delete schema (this removes all data)
            WeaviateSchemaManager.delete_camera_manual_schema()

            # Recreate schema
            WeaviateSchemaManager.create_camera_manual_schema()

            logger.info("🔄 System reset completed")
            return True
        except Exception as e:
            logger.error(f"❌ Error resetting system: {e}")
            return False

# Example usage and testing
def main():
    """Example usage of the Camera RAG System"""

    # Initialize system
    rag_system = CameraRAGSystem()

    if not rag_system.initialize_system():
        print("❌ Failed to initialize system")
        return

    # Example: Process a manual
    # rag_system.process_manual_file("path/to/Genie_Nano5G_Manual.pdf")

    # Example: Query the system
    result = rag_system.query_manuals(
        query="What are the specifications of M2050 camera?",
        model_name="M2050"
    )

    print("Query Result:")
    print(f"Answer: {result['answer']}")
    print(f"Source Files: {result['source_files']}")
    print(f"Models Found: {result['models_found']}")

if __name__ == "__main__":
    main()
