"""
Django management command to setup and manage the Camera RAG system
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import os
import sys

class Command(BaseCommand):
    help = 'Setup and manage the Camera RAG system for enhanced manual processing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=['setup', 'process', 'reset', 'stats', 'test'],
            default='setup',
            help='Action to perform: setup, process, reset, stats, or test'
        )
        
        parser.add_argument(
            '--pdf-id',
            type=int,
            help='Process specific PDF by ID'
        )
        
        parser.add_argument(
            '--test-query',
            type=str,
            help='Test query for the RAG system'
        )
        
        parser.add_argument(
            '--model-name',
            type=str,
            help='Filter by specific camera model'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        try:
            # Import the camera RAG integration
            from chatbot.camera_rag_integration import (
                EnhancedCameraRAG, 
                CameraRAGManagementCommands,
                process_camera_manual_from_db
            )
        except ImportError as e:
            raise CommandError(f"Failed to import Camera RAG system: {e}")

        if action == 'setup':
            self.setup_system()
        elif action == 'process':
            self.process_manuals(options.get('pdf_id'))
        elif action == 'reset':
            self.reset_system()
        elif action == 'stats':
            self.show_stats()
        elif action == 'test':
            self.test_system(options.get('test_query'), options.get('model_name'))

    def setup_system(self):
        """Setup the Camera RAG system"""
        self.stdout.write(self.style.SUCCESS('🚀 Setting up Camera RAG System...'))
        
        try:
            from chatbot.camera_rag_integration import CameraRAGManagementCommands
            success = CameraRAGManagementCommands.setup_system()
            
            if success:
                self.stdout.write(self.style.SUCCESS('✅ Camera RAG System setup completed successfully!'))
                self.stdout.write('Next steps:')
                self.stdout.write('1. Run: python manage.py setup_camera_rag --action=process')
                self.stdout.write('2. Test: python manage.py setup_camera_rag --action=test --test-query="camera specifications"')
            else:
                self.stdout.write(self.style.ERROR('❌ Setup failed'))
                
        except Exception as e:
            raise CommandError(f'Setup failed: {e}')

    def process_manuals(self, pdf_id=None):
        """Process camera manuals"""
        if pdf_id:
            self.stdout.write(f'📄 Processing PDF ID: {pdf_id}')
            try:
                from chatbot.camera_rag_integration import process_camera_manual_from_db
                result = process_camera_manual_from_db(pdf_id)
                
                if result['success']:
                    self.stdout.write(self.style.SUCCESS(
                        f'✅ Processed {result["filename"]}: '
                        f'{result["chunks_stored"]} chunks stored'
                    ))
                else:
                    self.stdout.write(self.style.ERROR(f'❌ Failed: {result["error"]}'))
                    
            except Exception as e:
                raise CommandError(f'Processing failed: {e}')
        else:
            self.stdout.write('📚 Processing all camera manuals from database...')
            try:
                from chatbot.camera_rag_integration import CameraRAGManagementCommands
                results = CameraRAGManagementCommands.process_all_manuals()
                
                if results.get('success', True):
                    self.stdout.write(self.style.SUCCESS(
                        f'✅ Processed {results["processed"]}/{results["total_files"]} files'
                    ))
                    self.stdout.write(f'📊 Total chunks created: {results["total_chunks"]}')
                    
                    # Show details
                    for detail in results.get('details', []):
                        filename = detail['filename']
                        result = detail['result']
                        if result['success']:
                            self.stdout.write(f'  ✅ {filename}: {result["chunks_stored"]} chunks')
                        else:
                            self.stdout.write(f'  ❌ {filename}: {result["error"]}')
                else:
                    self.stdout.write(self.style.ERROR(f'❌ Processing failed: {results.get("error")}'))
                    
            except Exception as e:
                raise CommandError(f'Processing failed: {e}')

    def reset_system(self):
        """Reset the Camera RAG system"""
        self.stdout.write(self.style.WARNING('⚠️  Resetting Camera RAG System (this will delete all data)...'))
        
        confirm = input('Are you sure? Type "yes" to confirm: ')
        if confirm.lower() != 'yes':
            self.stdout.write('Reset cancelled.')
            return
        
        try:
            from chatbot.camera_rag_integration import CameraRAGManagementCommands
            success = CameraRAGManagementCommands.reset_system()
            
            if success:
                self.stdout.write(self.style.SUCCESS('✅ System reset completed'))
            else:
                self.stdout.write(self.style.ERROR('❌ Reset failed'))
                
        except Exception as e:
            raise CommandError(f'Reset failed: {e}')

    def show_stats(self):
        """Show system statistics"""
        self.stdout.write('📊 Camera RAG System Statistics:')
        
        try:
            from chatbot.camera_rag_integration import EnhancedCameraRAG
            rag = EnhancedCameraRAG()
            stats = rag.get_system_statistics()
            
            if 'error' in stats:
                self.stdout.write(self.style.ERROR(f'❌ Error: {stats["error"]}'))
                return
            
            self.stdout.write(f'📄 Total chunks in Weaviate: {stats.get("total_chunks", 0)}')
            self.stdout.write(f'📁 Unique source files: {stats.get("unique_source_files", 0)}')
            self.stdout.write(f'📷 Unique camera types: {stats.get("unique_camera_types", 0)}')
            self.stdout.write(f'🗃️  PDF files in Django DB: {stats.get("django_pdf_files", 0)}')
            self.stdout.write(f'🎫 Support tickets: {stats.get("django_tickets", 0)}')
            
            if stats.get("camera_types"):
                self.stdout.write('\n📷 Camera Types:')
                for camera_type in stats["camera_types"]:
                    self.stdout.write(f'  • {camera_type}')
            
            if stats.get("source_files"):
                self.stdout.write('\n📁 Source Files:')
                for source_file in stats["source_files"][:10]:  # Show first 10
                    self.stdout.write(f'  • {source_file}')
                if len(stats["source_files"]) > 10:
                    self.stdout.write(f'  ... and {len(stats["source_files"]) - 10} more')
                    
        except Exception as e:
            raise CommandError(f'Failed to get statistics: {e}')

    def test_system(self, test_query=None, model_name=None):
        """Test the Camera RAG system"""
        if not test_query:
            test_query = "What are the specifications of the camera?"
        
        self.stdout.write(f'🧪 Testing Camera RAG System with query: "{test_query}"')
        if model_name:
            self.stdout.write(f'🔍 Filtering by model: {model_name}')
        
        try:
            from chatbot.camera_rag_integration import EnhancedCameraRAG
            rag = EnhancedCameraRAG()
            
            result = rag.rag_system.query_manuals(
                query=test_query,
                model_name=model_name,
                top_k=3
            )
            
            self.stdout.write('\n📋 Results:')
            self.stdout.write(f'Found {len(result["chunks"])} relevant chunks')
            
            if result["source_files"]:
                self.stdout.write(f'📁 Source files: {", ".join(result["source_files"])}')
            
            if result["models_found"]:
                self.stdout.write(f'📷 Models found: {", ".join(result["models_found"])}')
            
            self.stdout.write('\n🤖 Generated Answer:')
            self.stdout.write(result["answer"])
            
            if result["chunks"]:
                self.stdout.write('\n📄 Sample chunks:')
                for i, chunk in enumerate(result["chunks"][:2]):  # Show first 2 chunks
                    self.stdout.write(f'\nChunk {i+1}:')
                    self.stdout.write(f'  Source: {chunk.get("source_file", "Unknown")}')
                    self.stdout.write(f'  Section: {chunk.get("section_type", "general")}')
                    self.stdout.write(f'  Models: {", ".join(chunk.get("model_name", []))}')
                    content = chunk.get("content", "")[:200]
                    self.stdout.write(f'  Content: {content}...')
            
        except Exception as e:
            raise CommandError(f'Test failed: {e}')
