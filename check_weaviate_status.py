#!/usr/bin/env python3
"""
Check current Weaviate database status and classes
"""

import weaviate
import os

def check_weaviate_status():
    """Check what's currently in your Weaviate database."""
    print("🔍 Checking Current Weaviate Database Status")
    print("=" * 50)
    
    try:
        client = weaviate.Client("http://localhost:8080")
        
        if not client.is_ready():
            print("❌ Weaviate is not ready or not running")
            return
            
        print("✅ Weaviate is running and accessible")
        
        # Get schema
        schema = client.schema.get()
        classes = schema.get("classes", [])
        
        print(f"\n📊 Found {len(classes)} classes in Weaviate:")
        
        total_objects = 0
        for cls in classes:
            class_name = cls["class"]
            
            try:
                # Get object count
                result = client.query.aggregate(class_name).with_meta_count().do()
                count = result["data"]["Aggregate"][class_name][0]["meta"]["count"]
                total_objects += count
                
                # Get sample object to see structure
                try:
                    sample = client.query.get(class_name, ["source_file", "content"]).with_limit(1).do()
                    sample_objects = sample.get("data", {}).get("Get", {}).get(class_name, [])
                except:
                    sample_objects = []
                
                print(f"\n📋 Class: {class_name}")
                print(f"   📦 Objects: {count}")
                
                if sample_objects:
                    sample_obj = sample_objects[0]
                    if "source_file" in sample_obj:
                        print(f"   📄 Sample file: {sample_obj.get('source_file', 'N/A')}")
                    if "content" in sample_obj:
                        content_preview = sample_obj.get("content", "")[:100] + "..."
                        print(f"   📝 Sample content: {content_preview}")
                        
            except Exception as e:
                print(f"   ❌ Error getting data for {class_name}: {e}")
        
        print(f"\n📊 Total objects across all classes: {total_objects}")
        
        # Check if this looks like migrated data
        print("\n🔍 Analysis:")
        if any("AreaScan" in cls["class"] or "LineScan" in cls["class"] for cls in classes):
            print("✅ Found categorized classes (AreaScan, LineScan, etc.) - Migration appears complete")
        elif any("ChunkEmbeddings" in cls["class"] for cls in classes):
            print("⚠️ Found old-style classes (ChunkEmbeddings) - Migration may be needed")
        else:
            print("❓ No document classes found - Database may be empty")
            
    except Exception as e:
        print(f"❌ Error connecting to Weaviate: {e}")
        print("💡 Make sure Weaviate is running: docker-compose up -d")

def check_old_dump_folder():
    """Check if old Weaviate dump folder exists."""
    print("\n🗂️ Checking for Old Weaviate Dump Folder")
    print("=" * 50)
    
    # Common locations for old dump
    possible_locations = [
        "./weaviate-data",
        "./old-weaviate-data", 
        "./backup-weaviate",
        "../weaviate-backup"
    ]
    
    found_dump = False
    for location in possible_locations:
        if os.path.exists(location):
            print(f"📁 Found potential dump at: {location}")
            
            # Check if it has Weaviate structure
            expected_files = ["indexcount", "version"]
            has_weaviate_files = any(
                os.path.exists(os.path.join(location, f)) for f in expected_files
            )
            
            if has_weaviate_files:
                print(f"✅ {location} appears to be a Weaviate dump")
                
                # List subdirectories (these would be class names)
                try:
                    subdirs = [d for d in os.listdir(location) 
                             if os.path.isdir(os.path.join(location, d))]
                    print(f"   📋 Found classes: {subdirs}")
                    found_dump = True
                except:
                    pass
            else:
                print(f"❓ {location} exists but doesn't look like Weaviate dump")
    
    if not found_dump:
        print("❌ No old Weaviate dump found in common locations")
        print("💡 If you have an old dump, note its path for migration")

if __name__ == "__main__":
    check_weaviate_status()
    check_old_dump_folder()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY")
    print("=" * 50)
    print("1. Current Weaviate status shown above")
    print("2. If you see categorized classes (AreaScan, LineScan), migration is done")
    print("3. If you see old classes (ChunkEmbeddings), you need to migrate")
    print("4. New uploads will be stored in the correct category classes")
    print("5. Use the migration script if you have an old dump to migrate")
