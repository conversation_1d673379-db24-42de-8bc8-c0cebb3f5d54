"""
OpenAI Usage Tracking Module (Compatible with OpenAI Python 0.27.0)

This module provides wrapper functions to track OpenAI API usage
and automatically log usage data to the database for cost monitoring.
"""

import time
import openai
from decimal import Decimal
from django.utils import timezone
from chatbot.models import OpenAIUsage

# Set your API key for OpenAI legacy version
openai.api_key = "********************************************************************************************************************************************************************"

# OpenAI Pricing (as of 2024)
OPENAI_PRICING = {
    'gpt-4o-mini': {
        'input': Decimal('0.000150'),
        'output': Decimal('0.000600'),
    },
    'gpt-4o': {
        'input': Decimal('0.005000'),
        'output': Decimal('0.015000'),
    },
    'gpt-4': {
        'input': Decimal('0.030000'),
        'output': Decimal('0.060000'),
    },
    'gpt-3.5-turbo': {
        'input': Decimal('0.001500'),
        'output': Decimal('0.002000'),
    },
    'text-embedding-ada-002': {
        'input': Decimal('0.000100'),
        'output': Decimal('0.000000'),
    },
    'text-embedding-3-small': {
        'input': Decimal('0.000020'),
        'output': Decimal('0.000000'),
    },
    'text-embedding-3-large': {
        'input': Decimal('0.000130'),
        'output': Decimal('0.000000'),
    },
}


def calculate_cost(model_name, prompt_tokens, completion_tokens=0):
    if model_name not in OPENAI_PRICING:
        return Decimal('0.001000') * (prompt_tokens + completion_tokens) / 1000

    pricing = OPENAI_PRICING[model_name]
    input_cost = (Decimal(prompt_tokens) / 1000) * pricing['input']
    output_cost = (Decimal(completion_tokens) / 1000) * pricing['output']
    return input_cost + output_cost


def track_openai_chat_completion(model, messages, user=None, purpose="chat_response", **kwargs):
    """
    Wrapper for OpenAI ChatCompletion.create (legacy SDK) that tracks usage.
    """
    start_time = time.time()

    try:
        response = openai.ChatCompletion.create(
            model=model,
            messages=messages,
            **kwargs
        )

        response_time_ms = int((time.time() - start_time) * 1000)

        usage = response['usage'] if 'usage' in response else None
        prompt_tokens = usage['prompt_tokens'] if usage else 0
        completion_tokens = usage['completion_tokens'] if usage else 0
        total_tokens = usage['total_tokens'] if usage else (prompt_tokens + completion_tokens)

        cost = calculate_cost(model, prompt_tokens, completion_tokens)

        OpenAIUsage.objects.create(
            model_name=model,
            api_type='chat_completion',
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens,
            cost_usd=cost,
            user=user,
            request_timestamp=timezone.now(),
            response_time_ms=response_time_ms,
            purpose=purpose
        )

        return response

    except Exception as e:
        response_time_ms = int((time.time() - start_time) * 1000)
        OpenAIUsage.objects.create(
            model_name=model,
            api_type='chat_completion',
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            cost_usd=Decimal('0.00'),
            user=user,
            request_timestamp=timezone.now(),
            response_time_ms=response_time_ms,
            purpose=f"{purpose}_failed"
        )
        raise e


def track_openai_embedding(input_text, model="text-embedding-ada-002", user=None, purpose="embedding_generation", **kwargs):
    """
    Wrapper for OpenAI Embedding.create (legacy SDK) that tracks usage.
    """
    start_time = time.time()

    try:
        response = openai.Embedding.create(
            input=input_text,
            model=model,
            **kwargs
        )

        response_time_ms = int((time.time() - start_time) * 1000)

        usage = response['usage'] if 'usage' in response else None
        prompt_tokens = usage['total_tokens'] if usage else 0  # Embedding usage uses total_tokens
        total_tokens = prompt_tokens

        cost = calculate_cost(model, prompt_tokens, 0)

        OpenAIUsage.objects.create(
            model_name=model,
            api_type='embedding',
            prompt_tokens=prompt_tokens,
            completion_tokens=0,
            total_tokens=total_tokens,
            cost_usd=cost,
            user=user,
            request_timestamp=timezone.now(),
            response_time_ms=response_time_ms,
            purpose=purpose
        )

        return response

    except Exception as e:
        response_time_ms = int((time.time() - start_time) * 1000)
        OpenAIUsage.objects.create(
            model_name=model,
            api_type='embedding',
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            cost_usd=Decimal('0.00'),
            user=user,
            request_timestamp=timezone.now(),
            response_time_ms=response_time_ms,
            purpose=f"{purpose}_failed"
        )
        raise e


def track_openai_vision_completion(model, messages, user=None, purpose="vision_analysis", **kwargs):
    """
    Wrapper for OpenAI Vision request (same ChatCompletion.create with image URLs)
    """
    return track_openai_chat_completion(
        model=model,
        messages=messages,
        user=user,
        purpose=purpose,
        **kwargs
    )


def get_usage_summary(start_date=None, end_date=None):
    try:
        from django.db.models import Sum, Count, Avg

        queryset = OpenAIUsage.objects.all()

        if start_date:
            queryset = queryset.filter(request_timestamp__gte=start_date)
        if end_date:
            queryset = queryset.filter(request_timestamp__lte=end_date)

        summary = queryset.aggregate(
            total_requests=Count('id'),
            total_tokens_sum=Sum('total_tokens'),
            total_cost=Sum('cost_usd'),
            avg_tokens_per_request=Avg('total_tokens'),
            avg_cost_per_request=Avg('cost_usd'),
            total_prompt_tokens=Sum('prompt_tokens'),
            total_completion_tokens=Sum('completion_tokens'),
        )

        if 'total_tokens_sum' in summary:
            summary['total_tokens'] = summary.pop('total_tokens_sum')

        for key, value in summary.items():
            if value is None:
                summary[key] = 0
            elif hasattr(value, '_float_'):
                summary[key] = float(value)

        return summary

    except Exception:
        return {
            'total_requests': 0,
            'total_tokens': 0,
            'total_cost': 0.0,
            'avg_tokens_per_request': 0.0,
            'avg_cost_per_request': 0.0,
            'total_prompt_tokens': 0,
            'total_completion_tokens': 0,
        }