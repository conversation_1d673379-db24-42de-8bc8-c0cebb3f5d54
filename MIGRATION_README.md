# Weaviate Database Migration System

This system migrates your old Weaviate database dump into properly categorized classes and updates your Django application to use category-aware retrieval.

## 🎯 Overview

The migration system provides:

- **Data Migration**: Migrates old Weaviate dump into separate classes (AreaScan, LineScan, etc.)
- **Smart Upload**: Detects categories and avoids re-processing existing data
- **Categorized Retrieval**: Queries the correct Weaviate class based on product context
- **Future-Proof Architecture**: Easy to add new categories and maintain

## 📋 Prerequisites

1. **Docker** installed and running
2. **Python packages**: `weaviate-client`, `docker`, `django`
3. **Current Weaviate instance** running on `localhost:8080`
4. **Old Weaviate database dump** folder

## 🚀 Migration Steps

### Step 1: Install Dependencies

```bash
pip install weaviate-client docker python-dotenv
```

### Step 2: Run Database Migration

```bash
# First, do a dry run to see what will be migrated
python migrate_weaviate_dump.py --dump-path /path/to/old/weaviate/data --dry-run

# If everything looks good, execute the migration
python migrate_weaviate_dump.py --dump-path /path/to/old/weaviate/data --execute
```

### Step 3: Apply Django Migrations

```bash
python manage.py makemigrations
python manage.py migrate
```

### Step 4: Test the System

```bash
python test_migration_system.py
```

## 📁 New File Structure

```
chatbot/
├── migrate_weaviate_dump.py      # Main migration script
├── categorized_retrieval.py      # Category-aware retrieval system
├── category_manager.py           # Category management utilities
├── smart_upload.py              # Smart upload with duplicate detection
├── test_migration_system.py     # Testing and validation
└── migrations/
    └── 0025_add_document_category.py  # Django model updates
```

## 🏗️ Architecture

### Weaviate Classes

| Class Name | Purpose | Source |
|------------|---------|--------|
| `AreaScan` | Area scan camera documentation | Migrated + new uploads |
| `LineScan` | Line scan camera documentation | Migrated + new uploads |
| `FrameGrabber` | Frame grabber documentation | Migrated + new uploads |
| `Software` | SDK and software documentation | Migrated + new uploads |
| `GeneralChunks` | General/uncategorized content | Migrated + new uploads |

### Category Detection

The system automatically detects categories based on:

- **Filename patterns**: `areascan`, `linescan`, `framegrabber`, `sdk`, etc.
- **Content keywords**: Extracted from document text
- **Product hierarchy**: From support ticket context

### Retrieval Logic

1. **Ticket Context**: Uses `product_subcategory` and `product_type` from support tickets
2. **Product Context**: Uses product information from API calls
3. **Priority Search**: Searches relevant classes first, then falls back to others
4. **Result Ranking**: Combines certainty scores with class priority

## 🔧 Configuration

### Adding New Categories

```python
from chatbot.category_manager import get_category_manager

manager = get_category_manager()
manager.add_new_category(
    category_id="NewCategory",
    display_name="New Category Name",
    description="Description of the new category",
    keywords=["keyword1", "keyword2"],
    priority=50
)
```

### Updating Category Mappings

Edit `chatbot/categorized_retrieval.py`:

```python
PRODUCT_CATEGORY_MAPPING = {
    "Area Scan": "AreaScan",
    "Line Scan": "LineScan",
    "Your New Product": "YourNewCategory",  # Add here
    # ...
}
```

## 📊 Monitoring and Statistics

### Get Migration Statistics

```python
from chatbot.category_manager import get_category_manager

manager = get_category_manager()
stats = manager.get_class_statistics()
print(stats)
```

### Get Upload Statistics

```python
from chatbot.smart_upload import get_upload_statistics

stats = get_upload_statistics()
print(stats)
```

### Get Retrieval Statistics

```python
from chatbot.categorized_retrieval import get_retrieval_statistics

stats = get_retrieval_statistics()
print(stats)
```

## 🧪 Testing

The testing script validates:

- ✅ Weaviate connectivity
- ✅ Class structure integrity
- ✅ Data migration completeness
- ✅ Category detection accuracy
- ✅ Categorized retrieval functionality
- ✅ Ticket-based context retrieval
- ✅ Upload system functionality

## 🔍 Troubleshooting

### Migration Issues

**Problem**: Temporary Weaviate fails to start
```bash
# Check Docker logs
docker logs weaviate-migration-temp

# Ensure dump path is correct
ls -la /path/to/old/weaviate/data
```

**Problem**: Classes not created
```python
from chatbot.category_manager import get_category_manager
manager = get_category_manager()
manager.create_all_classes()
```

### Retrieval Issues

**Problem**: No results from categorized search
```python
# Check available classes
from chatbot.categorized_retrieval import get_categorized_retriever
retriever = get_categorized_retriever()
print(retriever.available_classes)

# Check class statistics
from chatbot.category_manager import get_category_manager
manager = get_category_manager()
print(manager.get_class_statistics())
```

### Upload Issues

**Problem**: Files not being categorized correctly
```python
from chatbot.category_manager import get_category_manager
manager = get_category_manager()

# Test category detection
category = manager.detect_category("your_filename.pdf", "sample content")
print(f"Detected category: {category}")
```

## 🔄 Maintenance

### Regular Tasks

1. **Monitor class sizes**: Check that classes aren't growing too large
2. **Update category keywords**: Add new keywords as products evolve
3. **Review uncategorized files**: Check `GeneralChunks` for miscategorized content
4. **Performance monitoring**: Monitor query response times

### Backup Strategy

```bash
# Backup current Weaviate data
docker exec weaviate-container tar -czf /backup/weaviate-$(date +%Y%m%d).tar.gz /var/lib/weaviate

# Backup Django database
python manage.py dumpdata > backup-$(date +%Y%m%d).json
```

## 📞 Support

If you encounter issues:

1. Run the test script: `python test_migration_system.py`
2. Check the logs for specific error messages
3. Verify your old dump structure matches expected format
4. Ensure all dependencies are installed correctly

## 🎉 Success Indicators

After successful migration, you should see:

- ✅ All tests passing in `test_migration_system.py`
- ✅ Multiple Weaviate classes with data
- ✅ Smart upload detecting existing files
- ✅ Category-specific search results
- ✅ Improved AI agent responses with relevant context
