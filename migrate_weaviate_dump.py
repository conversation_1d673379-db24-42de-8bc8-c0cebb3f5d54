#!/usr/bin/env python3
"""
Weaviate Database Dump Migration Script

This script migrates data from an old Weaviate database dump into properly categorized
classes in your current Weaviate instance.

Usage:
    python migrate_weaviate_dump.py --dump-path /path/to/old/weaviate/data --dry-run
    python migrate_weaviate_dump.py --dump-path /path/to/old/weaviate/data --execute
"""

import os
import sys
import json
import time
import argparse
import subprocess
import weaviate
from typing import Dict, List, Optional
import docker
from pathlib import Path

# Configuration
CURRENT_WEAVIATE_URL = "http://localhost:8080"
TEMP_WEAVIATE_URL = "http://localhost:8081"  # Temporary instance
TEMP_CONTAINER_NAME = "weaviate-migration-temp"

# Class mapping: old_class_name -> new_class_name
CLASS_MAPPING = {
    "areascanchunks": "AreaScan",
    "linescanchunks": "LineScan", 
    "chunkembeddingsv2": "GeneralChunks",  # fallback for unmapped data
    # Add more mappings as needed
}

# Category detection patterns
CATEGORY_PATTERNS = {
    "AreaScan": ["area scan", "area-scan", "areascan", "2d camera", "matrix camera"],
    "LineScan": ["line scan", "line-scan", "linescan", "linear camera", "1d camera"],
    "FrameGrabber": ["frame grabber", "framegrabber", "acquisition", "grabber"],
    "Software": ["software", "sdk", "api", "driver", "spinnaker", "sapera"],
}

class WeaviateMigrator:
    def __init__(self, dump_path: str, dry_run: bool = True):
        self.dump_path = Path(dump_path)
        self.dry_run = dry_run
        self.docker_client = docker.from_env()
        self.current_client = None
        self.temp_client = None
        self.temp_container = None
        
    def validate_dump_path(self) -> bool:
        """Validate that the dump path contains Weaviate data."""
        if not self.dump_path.exists():
            print(f"❌ Dump path does not exist: {self.dump_path}")
            return False
            
        # Check for Weaviate data structure
        expected_files = ["indexcount", "version"]
        has_expected = any((self.dump_path / f).exists() for f in expected_files)
        
        if not has_expected:
            print(f"❌ Path doesn't appear to contain Weaviate data: {self.dump_path}")
            return False
            
        print(f"✅ Valid Weaviate dump found at: {self.dump_path}")
        return True
        
    def setup_temp_weaviate(self) -> bool:
        """Start a temporary Weaviate instance with the old data."""
        try:
            print("🐳 Starting temporary Weaviate instance...")
            
            # Stop existing temp container if any
            try:
                existing = self.docker_client.containers.get(TEMP_CONTAINER_NAME)
                existing.stop()
                existing.remove()
                print("🗑️ Removed existing temporary container")
            except docker.errors.NotFound:
                pass
                
            # Start temporary Weaviate with old data mounted
            self.temp_container = self.docker_client.containers.run(
                "semitechnologies/weaviate:1.30.5",
                name=TEMP_CONTAINER_NAME,
                ports={'8080/tcp': 8081},
                volumes={
                    str(self.dump_path.absolute()): {'bind': '/var/lib/weaviate', 'mode': 'ro'}
                },
                environment={
                    'QUERY_DEFAULTS_LIMIT': '25',
                    'AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED': 'true',
                    'PERSISTENCE_DATA_PATH': '/var/lib/weaviate',
                    'DEFAULT_VECTORIZER_MODULE': 'none',
                    'CLUSTER_HOSTNAME': 'node1'
                },
                detach=True,
                remove=False
            )
            
            print("⏳ Waiting for temporary Weaviate to start...")
            time.sleep(15)
            
            # Test connection
            self.temp_client = weaviate.Client(TEMP_WEAVIATE_URL)
            if self.temp_client.is_ready():
                print("✅ Temporary Weaviate instance ready")
                return True
            else:
                print("❌ Temporary Weaviate failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Error starting temporary Weaviate: {e}")
            return False
            
    def connect_to_current_weaviate(self) -> bool:
        """Connect to the current Weaviate instance."""
        try:
            self.current_client = weaviate.Client(CURRENT_WEAVIATE_URL)
            if self.current_client.is_ready():
                print("✅ Connected to current Weaviate instance")
                return True
            else:
                print("❌ Current Weaviate instance not ready")
                return False
        except Exception as e:
            print(f"❌ Error connecting to current Weaviate: {e}")
            return False
            
    def analyze_old_data(self) -> Dict:
        """Analyze the structure and content of old Weaviate data."""
        print("\n📊 Analyzing old Weaviate data...")
        
        schema = self.temp_client.schema.get()
        classes = schema.get("classes", [])
        
        analysis = {
            "total_classes": len(classes),
            "classes": {},
            "total_objects": 0
        }
        
        for cls in classes:
            class_name = cls["class"]
            print(f"📋 Analyzing class: {class_name}")
            
            # Get object count
            try:
                result = self.temp_client.query.aggregate(class_name).with_meta_count().do()
                count = result["data"]["Aggregate"][class_name][0]["meta"]["count"]
            except:
                count = 0
                
            # Get sample objects to understand structure
            try:
                sample = self.temp_client.query.get(class_name).with_limit(3).do()
                sample_objects = sample.get("data", {}).get("Get", {}).get(class_name, [])
            except:
                sample_objects = []
                
            analysis["classes"][class_name] = {
                "count": count,
                "properties": cls.get("properties", []),
                "sample_objects": sample_objects
            }
            analysis["total_objects"] += count
            
            print(f"  📦 Objects: {count}")
            
        return analysis
        
    def detect_category_from_content(self, content: str, source_file: str = "") -> str:
        """Detect category based on content and filename."""
        content_lower = content.lower()
        source_lower = source_file.lower()
        
        for category, patterns in CATEGORY_PATTERNS.items():
            for pattern in patterns:
                if pattern in content_lower or pattern in source_lower:
                    return category
                    
        return "GeneralChunks"  # Default fallback
        
    def create_target_classes(self, analysis: Dict) -> bool:
        """Create target classes in current Weaviate instance."""
        print("\n🏗️ Creating target classes in current Weaviate...")
        
        # Determine which classes we need based on analysis
        needed_classes = set()
        
        for old_class, data in analysis["classes"].items():
            if old_class in CLASS_MAPPING:
                needed_classes.add(CLASS_MAPPING[old_class])
            else:
                # Try to detect category from sample data
                for sample in data["sample_objects"][:5]:
                    content = sample.get("content", "")
                    source_file = sample.get("source_file", "")
                    category = self.detect_category_from_content(content, source_file)
                    needed_classes.add(category)
                    break
                    
        # Add standard classes
        needed_classes.update(["AreaScan", "LineScan", "FrameGrabber", "Software", "GeneralChunks"])
        
        for class_name in needed_classes:
            if self.dry_run:
                print(f"🔍 [DRY RUN] Would create class: {class_name}")
                continue
                
            try:
                # Check if class already exists
                schema = self.current_client.schema.get()
                existing_classes = [c["class"] for c in schema.get("classes", [])]
                
                if class_name in existing_classes:
                    print(f"✅ Class '{class_name}' already exists")
                    continue
                    
                # Create class with standard schema
                class_schema = {
                    "class": class_name,
                    "description": f"Document chunks for {class_name} category",
                    "vectorizer": "none",
                    "properties": [
                        {"name": "source_file", "dataType": ["text"], "description": "Source file name"},
                        {"name": "chunk_number", "dataType": ["int"], "description": "Chunk number within file"},
                        {"name": "content", "dataType": ["text"], "description": "Text content of chunk"},
                        {"name": "category", "dataType": ["text"], "description": "Document category"},
                        {"name": "migrated_from", "dataType": ["text"], "description": "Original class name"},
                    ]
                }
                
                self.current_client.schema.create_class(class_schema)
                print(f"✅ Created class: {class_name}")
                
            except Exception as e:
                print(f"❌ Error creating class {class_name}: {e}")
                return False
                
        return True

    def migrate_class_data(self, old_class: str, analysis: Dict) -> bool:
        """Migrate data from old class to appropriate new classes."""
        print(f"\n🔄 Migrating data from class: {old_class}")

        class_data = analysis["classes"][old_class]
        total_objects = class_data["count"]

        if total_objects == 0:
            print(f"⏩ No objects to migrate from {old_class}")
            return True

        print(f"📦 Migrating {total_objects} objects...")

        # Batch processing
        batch_size = 100
        migrated_count = 0

        try:
            # Get all objects from old class
            cursor = None
            while True:
                query = self.temp_client.query.get(old_class, ["source_file", "chunk_number", "content"])

                if cursor:
                    query = query.with_after(cursor)

                result = query.with_limit(batch_size).with_additional(["vector", "id"]).do()

                objects = result.get("data", {}).get("Get", {}).get(old_class, [])
                if not objects:
                    break

                # Process batch
                for obj in objects:
                    if self.dry_run:
                        print(f"🔍 [DRY RUN] Would migrate object: {obj.get('source_file', 'unknown')}")
                        migrated_count += 1
                        continue

                    # Determine target class
                    content = obj.get("content", "")
                    source_file = obj.get("source_file", "")

                    if old_class in CLASS_MAPPING:
                        target_class = CLASS_MAPPING[old_class]
                    else:
                        target_class = self.detect_category_from_content(content, source_file)

                    # Prepare object for insertion
                    new_object = {
                        "source_file": source_file,
                        "chunk_number": obj.get("chunk_number", 0),
                        "content": content,
                        "category": target_class,
                        "migrated_from": old_class
                    }

                    # Get vector if available
                    vector = obj.get("_additional", {}).get("vector")

                    try:
                        # Insert into current Weaviate
                        self.current_client.data_object.create(
                            data_object=new_object,
                            class_name=target_class,
                            vector=vector
                        )
                        migrated_count += 1

                        if migrated_count % 50 == 0:
                            print(f"  📈 Migrated {migrated_count}/{total_objects} objects...")

                    except Exception as e:
                        print(f"❌ Error migrating object: {e}")
                        continue

                # Update cursor for next batch
                if objects:
                    cursor = objects[-1].get("_additional", {}).get("id")
                else:
                    break

        except Exception as e:
            print(f"❌ Error during migration: {e}")
            return False

        print(f"✅ Migrated {migrated_count} objects from {old_class}")
        return True

    def cleanup_temp_weaviate(self):
        """Clean up temporary Weaviate instance."""
        try:
            if self.temp_container:
                print("🧹 Cleaning up temporary Weaviate...")
                self.temp_container.stop()
                self.temp_container.remove()
                print("✅ Temporary Weaviate cleaned up")
        except Exception as e:
            print(f"⚠️ Error cleaning up: {e}")

    def run_migration(self) -> bool:
        """Run the complete migration process."""
        print("=" * 70)
        print("🚀 Weaviate Database Dump Migration")
        print("=" * 70)

        try:
            # Step 1: Validate dump path
            if not self.validate_dump_path():
                return False

            # Step 2: Setup temporary Weaviate
            if not self.setup_temp_weaviate():
                return False

            # Step 3: Connect to current Weaviate
            if not self.connect_to_current_weaviate():
                return False

            # Step 4: Analyze old data
            analysis = self.analyze_old_data()
            print(f"\n📊 Found {analysis['total_classes']} classes with {analysis['total_objects']} total objects")

            # Step 5: Create target classes
            if not self.create_target_classes(analysis):
                return False

            # Step 6: Migrate data
            for old_class in analysis["classes"]:
                if not self.migrate_class_data(old_class, analysis):
                    print(f"⚠️ Failed to migrate {old_class}, continuing...")

            print("\n" + "=" * 70)
            if self.dry_run:
                print("🔍 DRY RUN COMPLETED - No actual changes made")
                print("💡 Run with --execute to perform actual migration")
            else:
                print("🎉 MIGRATION COMPLETED SUCCESSFULLY!")
                print("💡 Your old data has been migrated to categorized classes")
            print("=" * 70)

            return True

        except Exception as e:
            print(f"❌ Migration failed: {e}")
            return False

        finally:
            self.cleanup_temp_weaviate()


def main():
    parser = argparse.ArgumentParser(description="Migrate Weaviate database dump to categorized classes")
    parser.add_argument("--dump-path", required=True, help="Path to old Weaviate database dump")
    parser.add_argument("--dry-run", action="store_true", help="Perform dry run without making changes")
    parser.add_argument("--execute", action="store_true", help="Execute actual migration")

    args = parser.parse_args()

    if not args.dry_run and not args.execute:
        print("❌ Must specify either --dry-run or --execute")
        sys.exit(1)

    if args.dry_run and args.execute:
        print("❌ Cannot specify both --dry-run and --execute")
        sys.exit(1)

    migrator = WeaviateMigrator(args.dump_path, dry_run=args.dry_run)
    success = migrator.run_migration()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
