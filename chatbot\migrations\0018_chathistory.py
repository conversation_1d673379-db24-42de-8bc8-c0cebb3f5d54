# Generated by Django 5.2.2 on 2025-07-12 11:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot', '0017_supportticket_brand_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_message', models.TextField(help_text="The user's message/query")),
                ('bot_response', models.TextField(help_text="The bot's response to the user message")),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When this chat exchange occurred')),
                ('ticket', models.ForeignKey(help_text='The support ticket this message belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='chat_messages', to='chatbot.supportticket')),
            ],
            options={
                'db_table': 'chat_history',
                'ordering': ['created_at'],
                'indexes': [models.Index(fields=['ticket', 'created_at'], name='chat_histor_ticket__8543cf_idx')],
            },
        ),
    ]
