{"ticket_number": "TCKT-RPHN0P9E", "prompt": "[PRODUCT DETAILS]\n- Product: Camera\n- Brand: DALSA\n- Sensor Type: Area Scan\n- Family: Genie Nano\n- Model: M1920\n- Serial Number: SN789012\n- SDK: Sapera LT (v8.70)\n- Programming Language: Python\n- Configuration Tool: CamExpert\n- Operating System: Windows 11\n\n[ISSUE CATEGORY]\n- Category: Detection, Frame Rate Issue\n\n[USER'S DESCRIPTION]\n\"My camera is not being detected by the system and the frame rate is very inconsistent. I've tried restarting but the issue persists.\" \n\n[CHAT HISTORY]\nFull previous conversation with the user:\n1. User: \"What specific steps should I take to check the camera drivers?\"\n   Bot: \"To check camera drivers: 1) Open Device Manager, 2) Look under 'Imaging devices', 3) Check for any warning signs or errors, 4) Update drivers if needed.\"\n\n[DOCUMENT CONTEXT]\n(Extracted chunks from uploaded user document or vector DB):\n(No relevant document context found)", "last_updated": "2025-07-12T11:33:16.916937+00:00"}