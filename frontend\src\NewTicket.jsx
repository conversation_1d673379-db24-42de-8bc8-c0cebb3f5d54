import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { BACKEND_URL } from "./utils/api";

export default function NewTicket() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    productType: "",
    purchasedFrom: "",
    yearOfPurchase: "",
    // Removed duplicates: productName, model, serialNo, operatingSystem
    // These are now handled by the detailed form fields
  });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const productTypeOptions = ["Camera", "Frame Grabber", "Accessories", "Software"];

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    const allFilled = Object.values(formData).every((v) => v.trim() !== "");
    if (!allFilled) {
      setError("Please fill in all required fields.");
      setLoading(false);
      return;
    }

    if (!productTypeOptions.includes(formData.productType)) {
      setError("Please select a valid product type.");
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("access")}`,
        },
        body: JSON.stringify({
          product_type: formData.productType,
          purchased_from: formData.purchasedFrom,
          year_of_purchase: formData.yearOfPurchase,
          // Removed duplicate fields - using detailed form instead
        }),
      });

      const data = await response.json();
      if (response.ok) {
        navigate(`/chatbot/${data.ticket_number}`);
      } else {
        setError(data.errors || "Failed to create ticket.");
      }
    } catch (err) {
      setError("Network error: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: "20px", maxWidth: "600px", margin: "0 auto" }}>
      <h1>Create New Support Ticket</h1>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: "15px" }}>
          <label style={{ display: "block", marginBottom: "5px" }}>
            Product Type:
            <select
              name="productType"
              value={formData.productType}
              onChange={handleChange}
              style={{ width: "100%", padding: "8px", borderRadius: "4px" }}
              required
            >
              <option value="" disabled>
                Select a product type
              </option>
              {productTypeOptions.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </label>
        </div>
        {[
          { label: "Purchased From", name: "purchasedFrom" },
          { label: "Year of Purchase", name: "yearOfPurchase" },
          // Removed duplicate fields: Product Name, Model, Serial Number, Operating System
          // These are now handled by the detailed camera-specific fields
        ].map((field) => (
          <div key={field.name} style={{ marginBottom: "15px" }}>
            <label style={{ display: "block", marginBottom: "5px" }}>
              {field.label}:
              <input
                type="text"
                name={field.name}
                value={formData[field.name]}
                onChange={handleChange}
                style={{ width: "100%", padding: "8px", borderRadius: "4px" }}
                required
              />
            </label>
          </div>
        ))}
        {error && (
          <div style={{ color: "red", marginBottom: "15px" }}>{error}</div>
        )}
        <button
          type="submit"
          disabled={loading}
          style={{
            padding: "10px 20px",
            backgroundColor: "#4CAF50",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: loading ? "not-allowed" : "pointer",
          }}
        >
          {loading ? "Submitting..." : "Submit Ticket"}
        </button>
      </form>
    </div>
  );
}