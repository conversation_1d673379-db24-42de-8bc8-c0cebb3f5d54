#!/usr/bin/env python3
"""
Update existing PdfFile records with categories based on filename detection.
Run this after applying the migration to categorize existing files.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_chatbot_backend.settings')
django.setup()

from chatbot.models import PdfFile
from chatbot.category_manager import get_category_manager


def update_existing_file_categories():
    """Update categories for existing PdfFile records."""
    print("🔄 Updating categories for existing PDF files...")
    
    category_manager = get_category_manager()
    
    # Get all files without categories or with default category
    files_to_update = PdfFile.objects.filter(
        category__in=['GeneralChunks', '']
    ) | PdfFile.objects.filter(category__isnull=True)
    
    total_files = files_to_update.count()
    print(f"📊 Found {total_files} files to categorize")
    
    if total_files == 0:
        print("✅ No files need category updates")
        return
    
    updated_count = 0
    category_counts = {}
    
    for pdf_file in files_to_update:
        # Detect category based on filename
        detected_category = category_manager.detect_category(pdf_file.file_name)
        
        # Update if different from current category
        if pdf_file.category != detected_category:
            old_category = pdf_file.category or 'None'
            pdf_file.category = detected_category
            pdf_file.save(update_fields=['category'])
            
            print(f"📝 {pdf_file.file_name}: {old_category} → {detected_category}")
            updated_count += 1
            
            # Count categories
            if detected_category in category_counts:
                category_counts[detected_category] += 1
            else:
                category_counts[detected_category] = 1
    
    print(f"\n✅ Updated {updated_count} files")
    print("📊 Category distribution:")
    for category, count in category_counts.items():
        print(f"  {category}: {count} files")


if __name__ == "__main__":
    update_existing_file_categories()
