import weaviate
from typing import Dict, List, Any

# ======== CONFIG ========
WEAVIATE_URL = "http://localhost:8080"  # or your endpoint
API_KEY = None  # for cloud: weaviate.auth.AuthApiKey(api_key="...")

DEST_CLASS = "CameraManualV4"  # ✅ New class name


# ======== CONNECT ========
client = weaviate.Client(
    url=WEAVIATE_URL,
    # auth_client_secret=weaviate.auth.AuthApiKey(API_KEY),  # uncomment for Cloud
)


# ======== DESIRED SCHEMA (V4, simplified) ========
def desired_class_schema() -> Dict[str, Any]:
    return {
        "class": DEST_CLASS,
        "description": "Enhanced camera manual chunks with metadata for multi-model and section-aware retrieval",
        "vectorizer": "none",
        "vectorIndexType": "hnsw",
        "vectorIndexConfig": {
            "distance": "cosine",
            "efConstruction": 128,
            "maxConnections": 64
        },
        "invertedIndexConfig": {
            "bm25": {"k1": 1.2, "b": 0.75},
            "stopwords": {"preset": "en"}
        },
        "properties": [
            # ---- Core document identity ----
            {
                "name": "source_file",
                "dataType": ["text"],
                "description": "Filename of the camera manual"
            },
            {
                "name": "file_hash",
                "dataType": ["text"],
                "description": "SHA256 hash of source file for deduplication"
            },

            # ---- Chunking + location ----
            {
                "name": "chunk_number",
                "dataType": ["int"],
                "description": "Sequential chunk ID within the document"
            },
            {
                "name": "page_number",
                "dataType": ["int"],
                "description": "Original PDF page number for precise reference"
            },
            {
                "name": "section_title",
                "dataType": ["text"],
                "description": "Nearest section or heading for this chunk"
            },

            # ---- Content ----
            {
                "name": "content",
                "dataType": ["text"],
                "description": "Chunked text content from the manual"
            },

            # ---- Domain/camera metadata ----
            {
                "name": "camera_type",
                "dataType": ["text"],
                "description": "Camera type (e.g., Nano-5G, Line-Scan, etc.)"
            },
            {
                "name": "model_name",
                "dataType": ["text[]"],
                "description": "Array of camera model names mentioned in this chunk"
            },
            {
                "name": "section_type",
                "dataType": ["text"],
                "description": "Type of section (overview, specifications, troubleshooting, etc.)"
            },

            # ---- Timestamps ----
            {
                "name": "created_at",
                "dataType": ["date"],
                "description": "Timestamp when the chunk was created"
            },
        ],
        "moduleConfig": {}
    }


# ======== HELPERS ========
def get_existing_classes() -> List[str]:
    schema = client.schema.get()
    return [c["class"] for c in schema.get("classes", [])]


def get_existing_properties(cls: str) -> Dict[str, Dict[str, Any]]:
    schema = client.schema.get()
    for c in schema.get("classes", []):
        if c["class"] == cls:
            return {p["name"]: p for p in c.get("properties", [])}
    return {}


def add_missing_properties(cls: str, desired_props: List[Dict[str, Any]]) -> List[str]:
    existing = get_existing_properties(cls)
    added = []
    for prop in desired_props:
        name = prop["name"]
        if name not in existing:
            client.schema.property.create(cls, prop)
            added.append(name)
    return added


# ======== CREATE / UPDATE ========
def ensure_camera_manual_v4_class() -> bool:
    try:
        existing_classes = get_existing_classes()
        spec = desired_class_schema()

        if DEST_CLASS not in existing_classes:
            client.schema.create_class(spec)
            print(f"✅ Created new class '{DEST_CLASS}' successfully (empty, ready for ingestion)")
            return True

        # Class exists: add any missing properties
        added = add_missing_properties(DEST_CLASS, spec["properties"])
        if added:
            print(f"🔧 Updated '{DEST_CLASS}' by adding missing properties: {added}")
        else:
            print(f"✅ Class '{DEST_CLASS}' already up-to-date")
        return True

    except Exception as e:
        print(f"❌ Error ensuring class '{DEST_CLASS}': {e}")
        return False


# ======== RUN ========
if __name__ == "__main__":
    ensure_camera_manual_v4_class()
