from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser, PdfFile
from django.utils.translation import gettext_lazy as _

class CustomUserAdmin(UserAdmin):
    model = CustomUser
    list_display = ('official_email', 'name', 'organization', 'is_staff', 'is_superuser')
    list_filter = ('state', 'is_staff', 'is_superuser')
    search_fields = ('official_email', 'name', 'organization')
    ordering = ('official_email',)

    fieldsets = (
        (None, {'fields': ('official_email', 'password')}),
        (_('Personal info'), {
            'fields': (
                'name', 'organization', 'address', 'state',
                'alt_email', 'phone', 'mobile'
            )
        }),
        (_('Permissions'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
        (_('Important dates'), {'fields': ('last_login',)}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': (
                'official_email', 'password1', 'password2',
                'name', 'organization', 'address', 'state',
                'alt_email', 'phone', 'mobile',
                'is_active', 'is_staff', 'is_superuser'
            ),
        }),
    )

admin.site.register(CustomUser, CustomUserAdmin)
admin.site.register(PdfFile)
from django.contrib import admin
from .models import PromptTemplate

admin.site.register(PromptTemplate)