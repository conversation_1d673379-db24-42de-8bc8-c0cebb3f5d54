import weaviate

client = weaviate.Client("http://localhost:8080")

schema = {
    "class": "CachedQuestions",
    "vectorizer": "none",  # we provide our own vector
    "properties": [
        {"name": "query_text", "dataType": ["text"]},
        {"name": "answer", "dataType": ["text"]},
    ]
}

if not client.schema.contains(schema):
    client.schema.create_class(schema)
    print("✅ 'CachedQuestions' class created.")
else:
    print("✅ 'CachedQuestions' class already exists.")
