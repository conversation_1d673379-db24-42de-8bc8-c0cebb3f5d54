import os
import django
import re

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ai_chatbot_backend.settings")
django.setup()

from chatbot.models import PdfFile, SupportTicket

def manual_keyword_match(user_data, top_n=1, debug=False):
    desc_parts = [
        user_data.get("product_type", ""),
        user_data.get("product_name", ""),
        user_data.get("model", ""),
        user_data.get("operating_system", ""),
        user_data.get("serial_no", ""),
        user_data.get("problem_description", "")
    ]
    combined_text = " ".join(part for part in desc_parts if part).lower()
    tokens = set(re.findall(r"\b\w+\b", combined_text))

    def keyword_matches_tokens(keyword, tokens):
        keyword_tokens = re.findall(r"\b\w+\b", keyword.lower())
        return all(kt in tokens for kt in keyword_tokens)

    if debug:
        print(f"\n🔍 Combined user input tokens ({len(tokens)}):\n{tokens}\n")

    scored_files = []

    for pdf in PdfFile.objects.all():
        if not pdf.keywords:
            if debug:
                print(f"Skipping PDF '{pdf.file_name}': no keywords found")
            continue

        keywords_lower = [kw.lower() for kw in pdf.keywords]

        if debug:
            print(f"PDF '{pdf.file_name}' keywords ({len(keywords_lower)}): {keywords_lower}")

        matched = [kw for kw in keywords_lower if keyword_matches_tokens(kw, tokens)]

        if debug:
            print(f"Matched keywords for '{pdf.file_name}': {matched}")

        score = len(matched) / len(keywords_lower) if keywords_lower else 0

        if debug:
            print(f"Match score for '{pdf.file_name}': {score:.3f}\n")

        scored_files.append((pdf.file_name, score, matched))

    scored_files.sort(key=lambda x: x[1], reverse=True)

    return scored_files[:top_n]


def get_user_input_from_ticket(ticket_number):
    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number)
    except SupportTicket.DoesNotExist:
        print(f"❌ Ticket '{ticket_number}' not found.")
        return None

    return {
        "product_type": ticket.product_type,
        "brand": ticket.brand,
        "model_number": ticket.model_number,
        "operating_system": ticket.operating_system_detailed,
        "serial_number": ticket.serial_number,
        "problem_description": ticket.problem_description or "",
    }

def match_ticket_to_pdfs(ticket_number, debug=False):
    user_input = get_user_input_from_ticket(ticket_number)
    if not user_input:
        return

    matches = manual_keyword_match(user_input, debug=debug)

    if not matches:
        print(f"⚠️ No matching PDF found for ticket {ticket_number}.")
        return

    print(f"\n📄 Top matching PDF(s) for ticket {ticket_number}:\n")
    for file_name, score, matched_keywords in matches:
        print(f"- {file_name} (score: {score:.3f}) matched keywords: {matched_keywords}")

if __name__ == "__main__":
    # Set debug=True to enable debug output
    match_ticket_to_pdfs("TCKT-WYMW94OW", debug=True)
