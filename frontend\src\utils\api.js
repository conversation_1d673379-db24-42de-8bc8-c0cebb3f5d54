const BACKEND_URL = "http://localhost:8000";

// Utility function to make authenticated API calls with automatic token refresh
export const apiCall = async (url, options = {}) => {
  const accessToken = localStorage.getItem("access");
  const refreshToken = localStorage.getItem("refresh");

  // Prepare headers
  const headers = {
    "Content-Type": "application/json",
    ...options.headers,
  };

  // Add authorization header if token exists
  if (accessToken) {
    headers.Authorization = `Bearer ${accessToken}`;
  }

  // Make the initial request
  let response = await fetch(url, {
    ...options,
    headers,
  });

  // If we get a 401 and have a refresh token, try to refresh
  if (response.status === 401 && refreshToken) {
    try {
      const refreshResponse = await fetch(`${BACKEND_URL}/api/token/refresh/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ refresh: refreshToken }),
      });

      if (refreshResponse.ok) {
        const refreshData = await refreshResponse.json();
        localStorage.setItem("access", refreshData.access);
        
        // Retry the original request with new token
        headers.Authorization = `Bearer ${refreshData.access}`;
        response = await fetch(url, {
          ...options,
          headers,
        });
      } else {
        // Refresh failed, clear tokens and redirect to login
        localStorage.removeItem("access");
        localStorage.removeItem("refresh");
        localStorage.removeItem("userData");
        window.location.href = "/auth";
        return null;
      }
    } catch (error) {
      console.error("Token refresh failed:", error);
      localStorage.removeItem("access");
      localStorage.removeItem("refresh");
      localStorage.removeItem("userData");
      window.location.href = "/auth";
      return null;
    }
  }

  return response;
};

// Wrapper for GET requests
export const apiGet = (url, options = {}) => {
  return apiCall(url, { method: "GET", ...options });
};

// Wrapper for POST requests
export const apiPost = (url, data, options = {}) => {
  return apiCall(url, {
    method: "POST",
    body: JSON.stringify(data),
    ...options,
  });
};

// Wrapper for PUT requests
export const apiPut = (url, data, options = {}) => {
  return apiCall(url, {
    method: "PUT",
    body: JSON.stringify(data),
    ...options,
  });
};

// Wrapper for DELETE requests
export const apiDelete = (url, options = {}) => {
  return apiCall(url, { method: "DELETE", ...options });
};

export { BACKEND_URL };
