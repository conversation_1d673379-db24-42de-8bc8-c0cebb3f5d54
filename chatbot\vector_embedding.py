import uuid
import mysql.connector
from typing import List
import weaviate
import openai
import argparse

# --- Configuration ---
WEAVIATE_URL = "http://localhost:8080"
CAMERA_MANUAL_CLASS = "CameraManualV4"  # Unified class for all manuals
EMBEDDING_BATCH_SIZE = 16  # process multiple at once

# OpenAI & Weaviate
openai.api_key = "********************************************************************************************************************************************************************"  # replace with env/config/ENV VAR
client_weaviate = weaviate.Client(WEAVIATE_URL)

# MySQL DB config
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'phoobesh333',
    'database': 'rough'
}

# ---------- DB Helpers ----------

def get_chunks_from_db() -> List[dict]:
    """Fetch unvectorized chunks from DB (Hybrid chunking already inserted)."""
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)
    cursor.execute("""
        SELECT id, source_file, chunk_number, content, camera_type,
               page_number, section_title, created_at
        FROM pdf_chunks
        WHERE vector_embedded = 0
    """)
    rows = cursor.fetchall()
    cursor.close()
    conn.close()
    return rows

def update_vectorized_flag(chunk_ids: List[int]):
    """Mark chunks as vectorized in DB."""
    if not chunk_ids:
        return
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    format_ids = ",".join(["%s"] * len(chunk_ids))
    cursor.execute(f"UPDATE pdf_chunks SET vector_embedded = 1 WHERE id IN ({format_ids})", chunk_ids)
    conn.commit()
    cursor.close()
    conn.close()

# ---------- Embedding Helpers ----------

def get_embeddings_batch(texts: List[str]) -> List[List[float]]:
    """Fetch embeddings from OpenAI API for a batch of texts."""
    response = openai.Embedding.create(
        input=texts,
        model="text-embedding-3-large"
    )
    return [item.embedding for item in response.data]

def batch_chunks(chunks: List[dict], batch_size: int):
    """Yield batches of chunks for efficient processing."""
    for i in range(0, len(chunks), batch_size):
        yield chunks[i:i + batch_size]

# ---------- Schema Setup ----------

def setup_weaviate_schema(client: weaviate.Client):
    """Ensure Weaviate schema has CameraManualV4 class."""
    schema = client.schema.get()
    existing_classes_lower = [c["class"].lower() for c in schema.get("classes", [])]

    if CAMERA_MANUAL_CLASS.lower() not in existing_classes_lower:
        new_class = {
            "class": CAMERA_MANUAL_CLASS,
            "description": "Unified storage for all camera manual document chunks",
            "properties": [
                {"name": "source_file", "dataType": ["string"], "description": "Filename of document"},
                {"name": "chunk_number", "dataType": ["int"], "description": "Index of the chunk"},
                {"name": "content", "dataType": ["text"], "description": "Actual chunk text"},
                {"name": "camera_type", "dataType": ["string"], "description": "Camera type"},
                {"name": "page_number", "dataType": ["int"], "description": "Page number from source PDF"},
                {"name": "section_title", "dataType": ["string"], "description": "Section heading/title"},
                {"name": "created_at", "dataType": ["string"], "description": "Insertion timestamp"}
            ],
            "vectorizer": "none"
        }
        client.schema.create_class(new_class)
        print(f"Created class '{CAMERA_MANUAL_CLASS}'.")

# ---------- Logic ----------

def stable_uuid(chunk: dict) -> str:
    """Stable UUID from source_file + chunk_number."""
    base = f"{chunk['source_file']}_{chunk['chunk_number']}"
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, base))

def store_embeddings(chunks: List[dict]):
    """Generate embeddings for chunks and push to Weaviate."""
    print(f"Processing {len(chunks)} chunks in batches of {EMBEDDING_BATCH_SIZE}...")

    for batch in batch_chunks(chunks, EMBEDDING_BATCH_SIZE):
        texts = [chunk["content"] for chunk in batch if chunk.get("content")]
        if not texts:
            continue

        try:
            embeddings = get_embeddings_batch(texts)
        except Exception as e:
            print(f"Embedding failed for batch: {e}")
            continue

        successful_ids = []
        for idx, chunk in enumerate(batch):
            try:
                embedding = embeddings[idx]
                obj_uuid = stable_uuid(chunk)

                # Skip if already exists
                if client_weaviate.data_object.exists(uuid=obj_uuid):
                    print(f"Already exists: {chunk['chunk_number']} from {chunk['source_file']}")
                    continue

                created_at_value = chunk.get("created_at")
                if created_at_value and not isinstance(created_at_value, str):
                        # Convert datetime → RFC3339 string (no microseconds, with UTC "Z")
                        created_at_value = created_at_value.replace(microsecond=0).isoformat() + "Z"
                        # convert datetime → string
                elif not created_at_value:
                        created_at_value = ""

                data_object = {
                        "source_file": chunk["source_file"],
                        "chunk_number": int(chunk["chunk_number"]),
                        "content": chunk["content"],
                        "camera_type": chunk.get("camera_type") or "unknown",
                        "page_number": chunk.get("page_number"),
                        "section_title": chunk.get("section_title") or "Unknown",
                        "created_at": created_at_value
                    }


                client_weaviate.data_object.create(
                    data_object=data_object,
                    class_name=CAMERA_MANUAL_CLASS,
                    vector=embedding,
                    uuid=obj_uuid
                )

                successful_ids.append(chunk["id"])
                print(f"✅ Vectorized chunk {chunk['chunk_number']} from {chunk['source_file']} → {CAMERA_MANUAL_CLASS}")
            except Exception as e:
                print(f"❌ Failed to vectorize chunk: {e}")
                continue

        update_vectorized_flag(successful_ids)

# ---------- Main Entry ----------

def main(args):
    if args.weaviate:
        setup_weaviate_schema(client_weaviate)
        chunks = get_chunks_from_db()
        if not chunks:
            print("No chunks to vectorize.")
            return

        store_embeddings(chunks)
        print("🎉 Vectorization complete.")
    else:
        print("Use --weaviate to start embedding.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--weaviate", action="store_true", help="Use Weaviate for vector storage")
    args = parser.parse_args()
    main(args)
