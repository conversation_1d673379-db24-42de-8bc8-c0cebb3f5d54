import weaviate
import random

# ===== CONFIG =====
WEAVIATE_URL = "http://localhost:8080"  # or cloud endpoint
API_KEY = None  # for cloud: weaviate.auth.AuthApiKey(api_key="...")
SOURCE_CLASS = "ChunkEmbeddingsV2"
DEST_CLASS = "ChunkEmbeddings"
SAMPLE_CHECKS = 5  # random spot checks

# ===== CONNECT =====
client = weaviate.Client(
    url=WEAVIATE_URL,
    # auth_client_secret=weaviate.auth.AuthApiKey(API_KEY),  # for cloud
)

# ===== FETCH IDs =====
def fetch_all_ids(class_name):
    ids = []
    offset = 0
    while True:
        query = (
            client.query
            .get(class_name, ["_additional { id }"])
            .with_limit(100)
            .with_offset(offset)
        )
        data = query.do()
        if "errors" in data:
            print(f"❌ Error fetching {class_name}:", data["errors"])
            break
        objs = data["data"]["Get"].get(class_name, [])
        if not objs:
            break
        ids.extend([o["_additional"]["id"] for o in objs])
        offset += 100
    return ids

print("Fetching all UUIDs...")
src_ids = fetch_all_ids(SOURCE_CLASS)
dst_ids = fetch_all_ids(DEST_CLASS)

# ===== 1. COUNT CHECK =====
print(f"\n🔍 Object counts:")
print(f"  {SOURCE_CLASS}: {len(src_ids)}")
print(f"  {DEST_CLASS}: {len(dst_ids)}")
count_ok = len(dst_ids) >= len(src_ids)

# ===== 2. UUID COVERAGE CHECK =====
missing_in_dest = set(src_ids) - set(dst_ids)
uuid_ok = len(missing_in_dest) == 0
print(f"  All source UUIDs present in destination: {uuid_ok}")
if not uuid_ok:
    print(f"  ❌ Missing {len(missing_in_dest)} UUIDs in destination.")
    print(f"  Example missing IDs: {list(missing_in_dest)[:5]}")

# ===== 3. SPOT-CHECK PROPERTIES =====
property_ok = True
if uuid_ok:
    sample_ids = random.sample(src_ids, min(SAMPLE_CHECKS, len(src_ids)))
    for uid in sample_ids:
        src_obj = client.data_object.get_by_id(uid, class_name=SOURCE_CLASS)
        dst_obj = client.data_object.get_by_id(uid, class_name=DEST_CLASS)
        if src_obj["properties"] != dst_obj["properties"]:
            print(f"  ❌ Property mismatch for UUID: {uid}")
            property_ok = False
            break

print(f"  Properties match: {property_ok}")

# ===== FINAL RESULT =====
if count_ok and uuid_ok and property_ok:
    print("\n✅ Verification PASSED: Destination contains all source objects with matching properties.")
else:
    print("\n❌ Verification FAILED: See above for details.")
