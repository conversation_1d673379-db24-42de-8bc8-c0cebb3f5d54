# Dependencies
node_modules/
frontend/node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
debug.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database
*.db
*.sqlite3

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
/build/
/dist/
frontend/build/
frontend/dist/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover

# Django
*.pot
local_settings.py
db.sqlite3
media/
staticfiles/

# React
frontend/build/
frontend/.eslintcache

# Temporary files
*.tmp
*.temp
.cache/

# Large files that shouldn't be in git
*.zip
*.tar.gz
*.rar
*.7z
*.dmg
*.iso

# Test files
test_*.py
debug_*.py


node_modules
# Ignore MANUALS folder
MANUALS/

# Ignore generated_image_pdfs folder
generated_image_pdfs/

*.pdf
*/.pdf