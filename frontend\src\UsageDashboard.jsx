import React, { useState, useEffect } from 'react';
import axios from 'axios';

const UsageDashboard = () => {
  const [summary, setSummary] = useState(null);
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    start_date: '',
    end_date: '',
    model_name: '',
    api_type: '',
    days: '30'
  });

  const token = localStorage.getItem('access');

  const fetchSummary = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.days) params.append('days', filters.days);
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);

      const response = await axios.get(`/api/usage/summary/?${params}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setSummary(response.data);
    } catch (err) {
      setError('Failed to fetch usage summary');
      console.error(err);
    }
  };

  const fetchLogs = async (page = 1) => {
    try {
      const params = new URLSearchParams();
      params.append('page', page);
      params.append('page_size', '20');
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);
      if (filters.model_name) params.append('model_name', filters.model_name);
      if (filters.api_type) params.append('api_type', filters.api_type);

      const response = await axios.get(`/api/usage/logs/?${params}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setLogs(response.data.logs);
      setCurrentPage(response.data.pagination.page);
      setTotalPages(response.data.pagination.total_pages);
    } catch (err) {
      setError('Failed to fetch usage logs');
      console.error(err);
    }
  };

  const exportCSV = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);
      if (filters.model_name) params.append('model_name', filters.model_name);
      if (filters.api_type) params.append('api_type', filters.api_type);

      const response = await axios.get(`/api/usage/export/?${params}`, {
        headers: { Authorization: `Bearer ${token}` },
        responseType: 'blob'
      });

      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `openai_usage_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('Failed to export CSV');
      console.error(err);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchSummary(), fetchLogs()]);
      setLoading(false);
    };
    loadData();
    // eslint-disable-next-line
  }, [filters.days, filters.start_date, filters.end_date]);

  useEffect(() => {
    fetchLogs(currentPage);
    // eslint-disable-next-line
  }, [filters.model_name, filters.api_type, currentPage]);

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    if (key !== 'days') {
      setCurrentPage(1);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(amount);
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '50vh',
        color: '#f3f4f6'
      }}>
        Loading usage data...
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        color: '#ef4444',
        textAlign: 'center',
        padding: '2rem',
        backgroundColor: '#1e1e2f'
      }}>
        Error: {error}
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: '#1e1e2f',
      minHeight: '100vh',
      overflowY: 'auto',
      padding: '2rem',
      color: '#f3f4f6'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <h1 style={{
          fontSize: '2rem',
          fontWeight: 'bold',
          marginBottom: '2rem',
          textAlign: 'center'
        }}>
          OpenAI Usage Dashboard
        </h1>

        {/* Filters */}
        <div style={{
          backgroundColor: '#2a2a40',
          padding: '1.5rem',
          borderRadius: '8px',
          marginBottom: '2rem'
        }}>
          <h3 style={{ marginBottom: '1rem' }}>Filters</h3>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem'
          }}>
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem' }}>Time Period</label>
              <select
                value={filters.days}
                onChange={(e) => handleFilterChange('days', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  borderRadius: '4px',
                  border: 'none',
                  backgroundColor: '#3b3b55',
                  color: '#f3f4f6'
                }}
              >
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="">All time</option>
              </select>
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem' }}>Model</label>
              <input
                type="text"
                placeholder="Filter by model name"
                value={filters.model_name}
                onChange={(e) => handleFilterChange('model_name', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  borderRadius: '4px',
                  border: 'none',
                  backgroundColor: '#3b3b55',
                  color: '#f3f4f6'
                }}
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem' }}>API Type</label>
              <select
                value={filters.api_type}
                onChange={(e) => handleFilterChange('api_type', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  borderRadius: '4px',
                  border: 'none',
                  backgroundColor: '#3b3b55',
                  color: '#f3f4f6'
                }}
              >
                <option value="">All types</option>
                <option value="chat_completion">Chat Completion</option>
                <option value="embedding">Embedding</option>
              </select>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        {summary && (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem',
            marginBottom: '2rem'
          }}>
            <div style={{
              backgroundColor: '#2a2a40',
              padding: '1.5rem',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h3 style={{ color: '#10b981', marginBottom: '0.5rem' }}>Total Cost</h3>
              <p style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>
                {formatCurrency(summary.summary.total_cost)}
              </p>
            </div>
            <div style={{
              backgroundColor: '#2a2a40',
              padding: '1.5rem',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h3 style={{ color: '#3b82f6', marginBottom: '0.5rem' }}>Total Requests</h3>
              <p style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>
                {formatNumber(summary.summary.total_requests)}
              </p>
            </div>
            <div style={{
              backgroundColor: '#2a2a40',
              padding: '1.5rem',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h3 style={{ color: '#f59e0b', marginBottom: '0.5rem' }}>Total Tokens</h3>
              <p style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>
                {formatNumber(summary.summary.total_tokens)}
              </p>
            </div>
            <div style={{
              backgroundColor: '#2a2a40',
              padding: '1.5rem',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h3 style={{ color: '#8b5cf6', marginBottom: '0.5rem' }}>Avg Cost/Request</h3>
              <p style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>
                {formatCurrency(summary.summary.avg_cost_per_request)}
              </p>
            </div>
          </div>
        )}

        {/* Export Button */}
        <div style={{ marginBottom: '2rem', textAlign: 'right' }}>
          <button
            onClick={exportCSV}
            style={{
              backgroundColor: '#10b981',
              color: 'white',
              padding: '0.75rem 1.5rem',
              borderRadius: '6px',
              border: 'none',
              cursor: 'pointer',
              fontWeight: '600'
            }}
          >
            Export CSV
          </button>
        </div>

        {/* Usage Logs Table */}
        <div style={{
          backgroundColor: '#2a2a40',
          borderRadius: '8px',
          overflow: 'visible'
        }}>
          <h3 style={{ padding: '1.5rem', margin: 0, borderBottom: '1px solid #3b3b55' }}>
            Usage Logs
          </h3>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#3b3b55' }}>
                  <th style={{ padding: '1rem', textAlign: 'left' }}>Timestamp</th>
                  <th style={{ padding: '1rem', textAlign: 'left' }}>Model</th>
                  <th style={{ padding: '1rem', textAlign: 'left' }}>Type</th>
                  <th style={{ padding: '1rem', textAlign: 'right' }}>Tokens</th>
                  <th style={{ padding: '1rem', textAlign: 'right' }}>Cost</th>
                  <th style={{ padding: '1rem', textAlign: 'left' }}>User</th>
                  <th style={{ padding: '1rem', textAlign: 'left' }}>Purpose</th>
                </tr>
              </thead>
              <tbody>
                {logs.map((log, index) => (
                  <tr key={log.id} style={{
                    borderBottom: '1px solid #3b3b55',
                    backgroundColor: index % 2 === 0 ? 'transparent' : '#2d2d45'
                  }}>
                    <td style={{ padding: '1rem' }}>
                      {new Date(log.request_timestamp).toLocaleString()}
                    </td>
                    <td style={{ padding: '1rem' }}>{log.model_name}</td>
                    <td style={{ padding: '1rem' }}>
                      <span style={{
                        padding: '0.25rem 0.5rem',
                        borderRadius: '4px',
                        fontSize: '0.75rem',
                        backgroundColor: log.api_type === 'chat_completion' ? '#3b82f6' : '#10b981',
                        color: 'white'
                      }}>
                        {log.api_type}
                      </span>
                    </td>
                    <td style={{ padding: '1rem', textAlign: 'right' }}>
                      {formatNumber(log.total_tokens)}
                    </td>
                    <td style={{ padding: '1rem', textAlign: 'right' }}>
                      {formatCurrency(log.cost_usd)}
                    </td>
                    <td style={{ padding: '1rem' }}>{log.user_name || 'System'}</td>
                    <td style={{ padding: '1rem' }}>{log.purpose}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div style={{
              padding: '1rem',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '1rem',
              borderTop: '1px solid #3b3b55'
            }}>
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                style={{
                  padding: '0.5rem 1rem',
                  borderRadius: '4px',
                  border: 'none',
                  backgroundColor: currentPage === 1 ? '#6b7280' : '#3b82f6',
                  color: 'white',
                  cursor: currentPage === 1 ? 'not-allowed' : 'pointer'
                }}
              >
                Previous
              </button>
              <span>Page {currentPage} of {totalPages}</span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                style={{
                  padding: '0.5rem 1rem',
                  borderRadius: '4px',
                  border: 'none',
                  backgroundColor: currentPage === totalPages ? '#6b7280' : '#3b82f6',
                  color: 'white',
                  cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'
                }}
              >
                Next
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UsageDashboard;
