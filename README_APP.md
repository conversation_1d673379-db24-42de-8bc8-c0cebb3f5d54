# 🤖 AI Agent Chatbot - Vector Search

A powerful AI-powered chatbot with vector search capabilities using OpenAI embeddings and semantic similarity search.

## 🚀 Quick Start

### 1. Generate Vector Embeddings (Already Done!)
```bash
python vector_embedding.py
```
✅ **COMPLETED** - All 241 chunks processed and embeddings generated!

### 2. Start the Application (Automated)
```bash
python start_app.py
```
This will:
- Start the backend API server on port 5000
- Ask you to choose between React or HTML frontend
- Open the selected frontend in your browser
- Ready to search!

### 3. Manual Start Options

**Option A: React Frontend (Recommended)**
```bash
# Terminal 1: Start Backend
python backend_server.py

# Terminal 2: Start React App
cd frontend
npm start
```
Then open: http://localhost:3000

**Option B: Simple HTML Frontend**
```bash
# Start Backend
python backend_server.py
```
Then open: `simple_frontend.html` in your browser

## 📁 Files Overview

### Core Application
- `vector_embedding.py` - Generates embeddings for all PDF chunks ✅
- `backend_server.py` - Flask API server for search functionality ✅
- `simple_frontend.html` - Web interface for searching ✅
- `start_app.py` - One-click startup script ✅

### Data Files
- `chunks.json` - Processed PDF chunks (input) ✅
- `embeddings_complete.json` - Generated embeddings (output) ✅
- `embeddings_summary.json` - Processing statistics ✅

### Frontend (React - Optional)
- `frontend/` - React application (alternative frontend)
- `frontend/src/App.jsx` - React search component
- `frontend/package.json` - React dependencies

## 🔧 Features

### ✅ Vector Search
- **Semantic Search** - Find content by meaning, not just keywords
- **Similarity Scoring** - Results ranked by relevance (0-100%)
- **Cross-Document Search** - Search across all PDF files
- **Real-time Results** - Fast search using cosine similarity

### ✅ Web Interface
- **Simple HTML Frontend** - Works in any browser
- **React Frontend** - Modern React application (optional)
- **Responsive Design** - Works on desktop and mobile
- **Error Handling** - Clear error messages and status

## 🎯 Usage Examples

### Search Queries
Try these example searches:
- `"camera configuration"` - Find camera setup information
- `"frame rate settings"` - Locate frame rate documentation  
- `"installation guide"` - Get installation instructions
- `"troubleshooting"` - Find troubleshooting steps
- `"specifications"` - View technical specifications

### API Usage
```bash
# Search via API
curl -X POST http://localhost:5000/api/search/ \
  -H "Content-Type: application/json" \
  -d '{"query": "camera configuration"}'

# Check health
curl http://localhost:5000/api/health
```

## 📊 Current Status

### ✅ Embeddings Generated
- **Total Chunks**: 241
- **Success Rate**: 100% (241/241)
- **File Size**: ~11MB
- **Model**: OpenAI text-embedding-ada-002

### ✅ Backend Running
- **Status**: Active on http://localhost:5000
- **Embeddings Loaded**: 241 chunks
- **API Endpoints**: /api/search/, /api/health

### ✅ Frontend Available
- **Simple HTML**: simple_frontend.html (Ready to use!)
- **React App**: frontend/ (Optional, needs npm install)

## 🎉 Ready to Use!

Your AI Agent Chatbot with Vector Search is **FULLY FUNCTIONAL**! 

🔍 **Open simple_frontend.html in your browser and start searching!** 🚀

### Quick Test:
1. Open `simple_frontend.html` in your browser
2. Try searching for "camera configuration"
3. See semantic search results with similarity scores!

**Everything is working perfectly!** ✨
