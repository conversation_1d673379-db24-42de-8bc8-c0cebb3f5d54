import mysql.connector
import hashlib
import fitz  # PyMuPDF
import spacy
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# Load spaCy once
nlp = spacy.load("en_core_web_sm")

# MySQL connection setup
conn = mysql.connector.connect(
    host="localhost",
    user="root",
    password="phoobesh333",
    database="rough"
)
cursor = conn.cursor()

# ---------- MySQL Helpers ----------

def fetch_pdf_files_from_db():
    """Fetch PDF files stored as BLOBs from MySQL."""
    cursor.execute("SELECT id, file_name, file_data, camera_type FROM pdf_files")
    return cursor.fetchall()

def fetch_existing_hashes():
    """Fetch file_hashes already processed and stored in the database."""
    cursor.execute("SELECT DISTINCT file_hash FROM pdf_chunks")
    return set(row[0] for row in cursor.fetchall())

def insert_chunks_to_db(chunks):
    """Insert new PDF chunks into the MySQL database."""
    for chunk in chunks:
        cursor.execute("""
            INSERT INTO pdf_chunks
            (source_file, chunk_number, content, file_hash, last_modified,
             chunked, vector_embedded, camera_type, page_number, section_title, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            chunk['source_file'],
            int(chunk['chunk_number']),
            chunk['content'],
            chunk['file_hash'],
            chunk['last_modified'],
            chunk['status']['chunked'],
            chunk['status']['vector_embedded'],
            chunk.get('camera_type'),
            chunk['page_number'],
            chunk['section_title'],
            chunk['created_at']
        ))
    conn.commit()

# ---------- Utility Functions ----------

def calculate_sha256_from_bytes(data):
    """Calculate SHA-256 from byte content."""
    return hashlib.sha256(data).hexdigest()

def extract_blocks_from_pdf(pdf_bytes):
    """Extract text blocks with page numbers and candidate section titles."""
    pdf_document = fitz.open("pdf", pdf_bytes)
    results = []
    for page_number, page in enumerate(pdf_document, start=1):
        blocks = page.get_text("blocks")
        for block in blocks:
            text = block[4].strip()
            if not text:
                continue
            # Heuristic: uppercase lines with <8 words are section headings
            section_title = text if (text.isupper() and len(text.split()) <= 8) else None
            results.append({
                "page_number": page_number,
                "text": text,
                "section_title": section_title
            })
    pdf_document.close()
    return results

def split_with_spacy(text):
    """Split text into sentences using spaCy."""
    doc = nlp(text)
    return [sent.text.strip() for sent in doc.sents if sent.text.strip()]

def regroup_sentences(sentences, max_words=250):
    """Regroup sentences into chunks of ~max_words words."""
    chunks, current_chunk, word_count = [], [], 0
    for sent in sentences:
        words = sent.split()
        if word_count + len(words) <= max_words:
            current_chunk.append(sent)
            word_count += len(words)
        else:
            chunks.append(" ".join(current_chunk))
            current_chunk = [sent]
            word_count = len(words)
    if current_chunk:
        chunks.append(" ".join(current_chunk))
    return chunks

# ---------- Main PDF Chunking Logic ----------

def process_all_pdfs_from_db():
    pdf_records = fetch_pdf_files_from_db()
    if not pdf_records:
        print("No PDF records found in DB.")
        return

    existing_hashes = fetch_existing_hashes()
    new_chunks = []

    def process_record(record):
        id_, file_name, file_data, camera_type = record
        file_hash = calculate_sha256_from_bytes(file_data)
        last_modified_str = datetime.now().isoformat()
        created_at_str = datetime.utcnow().isoformat()

        if file_hash in existing_hashes:
            print(f"Skipping {file_name} (no content changes).")
            return []

        print(f"Processing {file_name} (Camera Type: {camera_type})...")

        blocks = extract_blocks_from_pdf(file_data)
        if not blocks:
            return []

        processed_chunks = []
        for block in blocks:
            sentences = split_with_spacy(block["text"])
            sentence_chunks = regroup_sentences(sentences, max_words=250)

            for chunk_text in sentence_chunks:
                processed_chunks.append({
                    "content": chunk_text,
                    "page_number": block["page_number"],
                    "section_title": block["section_title"] or "Unknown"
                })

        return [{
            "source_file": file_name,
            "chunk_number": f"{idx+1}",
            "content": chunk["content"],
            "file_hash": file_hash,
            "last_modified": last_modified_str,
            "created_at": created_at_str,
            "camera_type": camera_type,
            "page_number": chunk["page_number"],
            "section_title": chunk["section_title"],
            "status": {
                "chunked": True,
                "vector_embedded": False
            }
        } for idx, chunk in enumerate(processed_chunks)]

    with ThreadPoolExecutor() as executor:
        results = executor.map(process_record, pdf_records)
        for chunks in results:
            new_chunks.extend(chunks)

    if new_chunks:
        insert_chunks_to_db(new_chunks)
        print(f"Inserted {len(new_chunks)} new chunk(s).")
    else:
        print("No new chunks to insert.")

# ---------- Main Entry Point ----------

if __name__ == "__main__":
    process_all_pdfs_from_db()
