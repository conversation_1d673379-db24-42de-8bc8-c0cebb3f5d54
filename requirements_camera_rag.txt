# Enhanced Camera RAG System Requirements
# Install with: pip install -r requirements_camera_rag.txt

# Core dependencies
weaviate-client>=3.25.0
openai>=0.28.0
PyPDF2>=3.0.1

# Text processing
spacy>=3.4.0
nltk>=3.8

# Data handling
numpy>=1.21.0
pandas>=1.3.0

# Utilities
python-dotenv>=0.19.0
tqdm>=4.64.0

# Optional: For advanced text processing
# sentence-transformers>=2.2.0
# transformers>=4.21.0

# Development and testing
pytest>=7.0.0
pytest-django>=4.5.0
