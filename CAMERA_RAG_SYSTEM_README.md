# Enhanced Camera RAG System Documentation

## Overview

This enhanced RAG (Retrieval-Augmented Generation) system provides comprehensive support for handling multiple camera manuals with advanced metadata extraction, multi-model support, and intelligent querying capabilities using Weaviate as the vector database.

## 🚀 Key Features

### 1. **Weaviate Schema - CameraManual Class**
- **source_file**: Filename of the manual
- **chunk_number**: Sequential chunk ID
- **content**: Actual chunked text from the manual
- **camera_type**: Extracted from filename (e.g., "Nano-5G")
- **model_name**: Array of models mentioned in chunk (e.g., ["M2050", "M2450"])
- **section_type**: Type of section (overview, specifications, troubleshooting, etc.)
- **file_hash**: SHA256 hash for deduplication
- **created_at**: Timestamp
- **External embeddings**: Using text-embedding-ada-002

### 2. **Advanced Metadata Extraction**
- **Camera Type**: Extracted from filename patterns
  - `Genie_Nano5G_Manual.pdf` → `Nano-5G`
  - `Linea_HS_UserGuide.pdf` → `Linea-HS`
- **Model Names**: Extracted from content using regex patterns
  - Supports formats: M2050, C2450, BFS-U3-31S4M-C, etc.
  - Multiple models per chunk stored as arrays

### 3. **Intelligent Chunking Strategy**
- **Section-based chunking**: Identifies logical sections
  - Overview, Specifications, Installation, Configuration
  - Firmware, Troubleshooting, Maintenance
- **Preserves model associations** across chunks
- **Configurable chunk size** with context preservation

### 4. **Multi-Model Query Support**
- **Camera type filtering**: Search within specific camera series
- **Model-specific queries**: Target individual camera models
- **Section filtering**: Focus on specific document sections
- **Hybrid search**: Combines vector similarity with metadata filtering

## 📁 File Structure

```
├── camera_rag_system.py              # Core RAG system implementation
├── chatbot/
│   ├── camera_rag_integration.py     # Django integration
│   ├── management/
│   │   └── commands/
│   │       └── setup_camera_rag.py   # Management command
│   └── views.py                      # Updated with RAG integration
├── test_camera_rag_system.py         # Comprehensive test suite
├── requirements_camera_rag.txt       # Dependencies
└── CAMERA_RAG_SYSTEM_README.md       # This documentation
```

## 🛠️ Installation & Setup

### 1. Install Dependencies
```bash
pip install -r requirements_camera_rag.txt
```

### 2. Start Weaviate
```bash
docker-compose up -d
```

### 3. Set Environment Variables
```bash
export OPENAI_API_KEY="your-openai-api-key"
```

### 4. Initialize System
```bash
python manage.py setup_camera_rag --action=setup
```

### 5. Process Manuals
```bash
# Process all PDFs from database
python manage.py setup_camera_rag --action=process

# Process specific PDF
python manage.py setup_camera_rag --action=process --pdf-id=1
```

## 🧪 Testing

### Run Comprehensive Tests
```bash
python test_camera_rag_system.py
```

### Test Specific Components
```bash
# Test with management command
python manage.py setup_camera_rag --action=test --test-query="camera specifications" --model-name="M2050"
```

## 📊 Usage Examples

### 1. Basic Query
```python
from camera_rag_system import CameraRAGSystem

rag = CameraRAGSystem()
rag.initialize_system()

result = rag.query_manuals("What are the specifications of M2050?")
print(result['answer'])
```

### 2. Filtered Query
```python
# Search within specific camera type
result = rag.query_manuals(
    query="troubleshooting connection issues",
    camera_type="Nano-5G",
    model_name="M2050"
)
```

### 3. Django Integration
```python
from chatbot.camera_rag_integration import EnhancedCameraRAG

rag = EnhancedCameraRAG()

# Process PDF from database
result = rag.process_pdf_from_database(pdf_id=1)

# Enhanced search with ticket context
result = rag.enhanced_search_for_ticket(ticket, "camera not detected")
```

## 🔍 Query Capabilities

### 1. **Vector Search with Metadata Filtering**
```python
# Search by camera type
chunks = query_engine.search_by_camera_series("specifications", "Nano-5G")

# Search specific model
chunks = query_engine.search_specific_model("troubleshooting", "M2050")

# Get model specifications
specs = query_engine.get_model_specifications("M2050")
```

### 2. **Advanced Filtering Options**
- **camera_type**: Filter by camera series (e.g., "Nano-5G")
- **model_name**: Filter by specific model (supports arrays)
- **section_type**: Filter by document section
- **certainty_threshold**: Minimum similarity score

### 3. **Multi-Model Support**
- Chunks can contain multiple models: `["M2050", "M2450"]`
- Queries return results for any matching model in the array
- Supports complex model relationships within documents

## 📈 System Management

### Get Statistics
```bash
python manage.py setup_camera_rag --action=stats
```

### Reset System
```bash
python manage.py setup_camera_rag --action=reset
```

### Monitor Performance
```python
from chatbot.camera_rag_integration import EnhancedCameraRAG

rag = EnhancedCameraRAG()
stats = rag.get_system_statistics()
print(f"Total chunks: {stats['total_chunks']}")
print(f"Camera types: {stats['camera_types']}")
```

## 🔧 Configuration Options

### Chunking Parameters
- **max_chunk_size**: Maximum tokens per chunk (default: 1000)
- **section_overlap**: Overlap between sections (configurable)

### Search Parameters
- **top_k**: Number of results to retrieve (default: 5)
- **certainty_threshold**: Minimum similarity score (default: 0.7)

### Embedding Settings
- **model**: text-embedding-ada-002 (OpenAI)
- **batch_size**: 100 (for batch processing)

## 🚨 Error Handling & Troubleshooting

### Common Issues

1. **Weaviate Connection Failed**
   ```bash
   # Check if Weaviate is running
   docker ps | grep weaviate
   
   # Restart if needed
   docker-compose restart weaviate
   ```

2. **OpenAI API Errors**
   ```bash
   # Verify API key
   echo $OPENAI_API_KEY
   
   # Check API quota and billing
   ```

3. **No Results Found**
   - Lower certainty_threshold
   - Check if manuals are processed
   - Verify model names in content

### Debug Mode
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🔄 Integration with Existing System

### Backward Compatibility
- Enhanced system maintains compatibility with existing `search_similar_chunks_weaviate`
- Automatic fallback to legacy system if enhanced RAG unavailable
- Gradual migration path for existing functionality

### Ticket Integration
- Automatic extraction of camera context from support tickets
- Enhanced queries using ticket metadata
- Improved relevance for support responses

## 📋 Performance Optimization

### Indexing
- Weaviate handles vector indexing automatically
- Metadata fields indexed for fast filtering
- Batch processing for large document sets

### Caching
- Embedding caching for repeated queries
- Result caching for common searches
- Efficient chunk retrieval

### Monitoring
- Track query performance
- Monitor embedding generation costs
- System health statistics

## 🔮 Future Enhancements

1. **Advanced NLP Features**
   - Named entity recognition for better model extraction
   - Semantic section classification
   - Multi-language support

2. **Enhanced Querying**
   - Hybrid search combining multiple strategies
   - Query expansion and refinement
   - Contextual follow-up questions

3. **Analytics & Insights**
   - Query analytics dashboard
   - Content gap analysis
   - User behavior insights

## 📞 Support

For issues or questions:
1. Check the test suite: `python test_camera_rag_system.py`
2. Review system stats: `python manage.py setup_camera_rag --action=stats`
3. Enable debug logging for detailed error information
