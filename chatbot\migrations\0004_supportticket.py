# Generated by Django 5.2.2 on 2025-06-21 05:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot', '0003_prompttemplate'),
    ]

    operations = [
        migrations.CreateModel(
            name='SupportTicket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticket_number', models.CharField(editable=False, max_length=20, unique=True)),
                ('purchased_from', models.CharField(max_length=255)),
                ('year_of_purchase', models.Char<PERSON>ield(max_length=10)),
                ('product_name', models.Char<PERSON>ield(max_length=255)),
                ('model', models.Char<PERSON>ield(max_length=255)),
                ('serial_no', models.CharField(max_length=255)),
                ('operating_system', models.Char<PERSON>ield(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
