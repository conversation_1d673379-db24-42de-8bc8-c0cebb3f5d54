import { useEffect, useState } from "react";
import axios from "axios";

export default function EscalatedTicketsPage() {
  const [tickets, setTickets] = useState([]);
  const [detail, setDetail] = useState(null);

  useEffect(() => {
    const fetchTickets = async () => {
      const token = localStorage.getItem("access");
      const { data } = await axios.get("/api/tickets/escalated/", {
        headers: { Authorization: `Bearer ${token}` },
      });
      setTickets(data);
    };
    fetchTickets();
  }, []);

  const openDetail = async (ticket_number) => {
    const token = localStorage.getItem("access");
    const { data } = await axios.get(`/api/tickets/escalated/${ticket_number}/`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    setDetail(data);
  };

  if (detail) {
    return (
      <div className="p-6">
        <button onClick={() => setDetail(null)} className="underline mb-4">
          ← Back to list
        </button>
        <h2 className="text-xl font-semibold mb-2">Ticket #{detail.ticket_number}</h2>
        <pre className="bg-gray-100 p-4 rounded-xl overflow-auto">{JSON.stringify(detail, null, 2)}</pre>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Escalated Tickets</h1>
      {tickets.length === 0 && <p>No escalated tickets 🎉</p>}
      <ul className="space-y-2">
        {tickets.map((t) => (
          <li
            key={t.ticket_number}
            className="cursor-pointer bg-white shadow p-4 rounded-xl hover:bg-gray-50"
            onClick={() => openDetail(t.ticket_number)}
          >
            <span className="font-mono">#{t.ticket_number}</span>
            <span className="text-sm text-gray-500 ml-2">{new Date(t.created_at).toLocaleString()}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}
