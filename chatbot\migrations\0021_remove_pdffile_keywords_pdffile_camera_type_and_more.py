# Generated by Django 5.2.2 on 2025-09-02 08:03

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot', '0020_supportticket_product_category_and_more'),
    ]

    operations = [
       
        migrations.AddField(
            model_name='pdffile',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.CreateModel(
            name='PdfChunk',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_file', models.CharField(max_length=255)),
                ('chunk_number', models.IntegerField()),
                ('content', models.TextField()),
                ('file_hash', models.CharField(blank=True, max_length=64, null=True)),
                ('last_modified', models.DateTimeField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('camera_type', models.CharField(blank=True, max_length=50, null=True)),
                ('page_number', models.IntegerField(blank=True, null=True)),
                ('section_title', models.CharField(default='Unknown', max_length=500)),
                ('chunked', models.BooleanField(default=True)),
                ('vector_embedded', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'pdf_chunks',
                'unique_together': {('file_hash', 'chunk_number')},
            },
        ),
    ]
