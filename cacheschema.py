import weaviate

CACHE_CLASS_NAME = "CachedQuestion"

def create_cached_question_schema():
    client = weaviate.Client("http://localhost:8080")  # Change URL if needed

    schema = client.schema.get()

    # Check if class already exists
    for cls in schema.get("classes", []):
        if cls["class"] == CACHE_CLASS_NAME:
            print(f"✅ '{CACHE_CLASS_NAME}' class already exists.")
            return

    # Define new class schema
    new_class = {
        "class": CACHE_CLASS_NAME,
        "vectorizer": "none",   # We provide embeddings manually
        "properties": [
            {
                "name": "text",
                "dataType": ["text"],
                "description": "User query text"
            },
            {
                "name": "answer",
                "dataType": ["text"],
                "description": "Answer text"
            },
            {
                "name": "source_files",
                "dataType": ["text[]"],  # Array of strings
                "description": "List of related source file names"
            },
        ],
        "vectorIndexConfig": {
            "type": "hnsw"
        }
    }

    client.schema.create_class(new_class)
    print(f"✅ '{CACHE_CLASS_NAME}' class created successfully.")

if __name__ == "__main__":
    create_cached_question_schema()
