#!/usr/bin/env python3
"""
Import embeddings from JSON file to Weaviate
"""

import json
import weaviate
from weaviate.util import generate_uuid5

# Configuration
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"
JSON_FILE_PATH = "embeddings_complete.json"

def create_collection(client):
    """Create the collection in Weaviate."""
    print(f"📝 Creating collection '{WEAVIATE_CLASS_NAME}'...")
    
    try:
        # Delete collection if it exists
        if client.collections.exists(WEAVIATE_CLASS_NAME):
            client.collections.delete(WEAVIATE_CLASS_NAME)
            print(f"🗑️  Deleted existing collection '{WEAVIATE_CLASS_NAME}'")
        
        # Create new collection
        collection = client.collections.create(
            name=WEAVIATE_CLASS_NAME,
            properties=[
                weaviate.classes.config.Property(
                    name="source_file",
                    data_type=weaviate.classes.config.DataType.TEXT,
                    description="Source file name"
                ),
                weaviate.classes.config.Property(
                    name="chunk_number",
                    data_type=weaviate.classes.config.DataType.INT,
                    description="Chunk number within the file"
                ),
                weaviate.classes.config.Property(
                    name="content",
                    data_type=weaviate.classes.config.DataType.TEXT,
                    description="Text content of the chunk"
                ),
            ],
            vectorizer_config=weaviate.classes.config.Configure.Vectorizer.none(),  # We'll provide our own vectors
        )
        
        print(f"✅ Created collection '{WEAVIATE_CLASS_NAME}'")
        return True
        
    except Exception as e:
        print(f"❌ Error creating collection: {e}")
        return False

def import_embeddings(client):
    """Import embeddings from JSON file to Weaviate."""
    print(f"📂 Loading embeddings from {JSON_FILE_PATH}...")
    
    try:
        # Load JSON data
        with open(JSON_FILE_PATH, "r", encoding="utf-8") as f:
            embeddings_data = json.load(f)
        
        print(f"✅ Loaded {len(embeddings_data)} embeddings")
        
        # Get the collection
        collection = client.collections.get(WEAVIATE_CLASS_NAME)
        
        # Prepare data for batch import
        objects = []
        for i, chunk in enumerate(embeddings_data):
            # Create object with properties and vector
            obj = weaviate.classes.data.DataObject(
                properties={
                    "source_file": chunk["source_file"],
                    "chunk_number": chunk["chunk_number"],
                    "content": chunk["content"]
                },
                vector=chunk["embedding"]  # Use the embedding as vector
            )
            objects.append(obj)
            
            # Show progress
            if (i + 1) % 50 == 0:
                print(f"   Prepared {i + 1}/{len(embeddings_data)} objects...")
        
        print(f"📤 Importing {len(objects)} objects to Weaviate...")
        
        # Batch import
        response = collection.data.insert_many(objects)
        
        # Check for errors
        if response.has_errors:
            print("⚠️  Some objects failed to import:")
            for error in response.errors:
                print(f"   Error: {error}")
        else:
            print(f"✅ Successfully imported {len(objects)} objects")
        
        # Verify import
        result = collection.aggregate.over_all(total_count=True)
        total_count = result.total_count
        print(f"🔍 Verification: Collection now has {total_count} objects")
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing embeddings: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("=" * 60)
    print("📥 Import Embeddings to Weaviate")
    print("=" * 60)
    
    try:
        # Connect to Weaviate
        print("🔌 Connecting to Weaviate...")
        with weaviate.connect_to_local() as client:
            print("✅ Connected to Weaviate")
            
            # Create collection
            if not create_collection(client):
                return
            
            # Import embeddings
            if not import_embeddings(client):
                return
            
            print("\n" + "=" * 60)
            print("🎉 Import completed successfully!")
            print("=" * 60)
            print("💡 You can now use the chatbot with Weaviate vector search")
            print("🚀 Start the application with: python start_weaviate_app.py")
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        print("💡 Make sure Weaviate is running: docker-compose up -d")

if __name__ == "__main__":
    main()
