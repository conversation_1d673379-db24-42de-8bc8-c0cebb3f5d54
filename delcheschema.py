import weaviate

client = weaviate.Client("http://localhost:8080")

OLD_CLASS = "CachedQuestions"

# Get list of all class names
schema = client.schema.get()
existing_classes = [cls["class"] for cls in schema.get("classes", [])]

# Check and delete
if OLD_CLASS in existing_classes:
    client.schema.delete_class(OLD_CLASS)
    print(f"🗑️ Deleted old class '{OLD_CLASS}'.")
else:
    print(f"ℹ️ Class '{OLD_CLASS}' does not exist.")
