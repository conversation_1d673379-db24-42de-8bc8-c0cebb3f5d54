# chatbot/management/commands/backfill_pdf_keywords.py

"""
backfill_pdf_keywords.py
Run once to extract real keywords from existing PdfFile records.
"""

import os
import io
import django

# 🔧 1) Point to your settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ai_chatbot_backend.settings")
django.setup()

from chatbot.models import PdfFile
from chatbot.utils.keywords import extract_keywords     # TF-IDF helper
from PyPDF2 import PdfReader                         # pip install PyPDF2

def pdf_bytes_to_text(pdf_bytes: bytes) -> str:
    """Extracts *visible* text from a PDF binary."""
    text = ""
    reader = PdfReader(io.BytesIO(pdf_bytes))
    for page in reader.pages:
        text += page.extract_text() or ""
    return text

def backfill():
    updated, skipped = 0, 0
    for pdf in PdfFile.objects.all():
        if pdf.keywords:         # already populated
            skipped += 1
            continue

        try:
            full_text = pdf_bytes_to_text(pdf.file_data)
            if not full_text.strip():
                # Fallback to filename tokens if PDF text is empty/scanned.
                keywords = pdf.file_name.lower().replace(".pdf", "").split()
            else:
                keywords = extract_keywords(full_text, top_n=20)

            pdf.keywords = keywords
            pdf.save()
            updated += 1
            print(f"✓ {pdf.file_name}: {keywords}")
        except Exception as err:
            print(f"⚠️  {pdf.file_name}: {err}")

    print(f"\nDone. {updated} updated, {skipped} already had keywords.")

if __name__ == "__main__":
    backfill()

