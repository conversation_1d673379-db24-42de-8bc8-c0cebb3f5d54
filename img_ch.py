import fitz  # PyMuPDF
import openai
import pytesseract
import mysql.connector
from fpdf import FPDF
from io import BytesIO
from PIL import Image, ImageEnhance

# --- Config ---
openai.api_key = "********************************************************************************************************************************************************************"
pytesseract.pytesseract.tesseract_cmd = r"D:\TesseractOCR\tesseract.exe"

db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'phoobesh333',
    'database': 'rough'
}

# --- Database Functions ---
def fetch_unsummarized_pdfs():
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    cursor.execute("SELECT id, file_name, file_data FROM pdf_image_pages WHERE summarized = FALSE")
    rows = cursor.fetchall()
    cursor.close()
    conn.close()
    return rows

def mark_as_summarized(pdf_id):
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    cursor.execute("UPDATE pdf_image_pages SET summarized = TRUE WHERE id = %s", (pdf_id,))
    conn.commit()
    cursor.close()
    conn.close()

def insert_summary_pdf_to_db(filename, pdf_bytes):
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO pdf_files (file_name, file_data, last_modified)
            VALUES (%s, %s, NOW())
        """, (filename, pdf_bytes))
        conn.commit()
        cursor.close()
        conn.close()
        print(f"📥 Saved summary PDF to DB as: {filename}")
    except Exception as e:
        print(f"❌ Failed to insert summary PDF into DB: {e}")

# --- Image/OCR Functions ---
def extract_pages_with_images(pdf_bytes):
    pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
    pages_data = []
    for page_num in range(len(pdf_doc)):
        page = pdf_doc[page_num]
        image_list = page.get_images(full=True)
        if image_list:
            print(f"📸 Found image(s) on page {page_num + 1}")
            full_page_img = page.get_pixmap(dpi=400)
            img_bytes = BytesIO(full_page_img.tobytes("png")).getvalue()
            pages_data.append({
                "page_number": page_num + 1,
                "image_bytes": img_bytes
            })
    pdf_doc.close()
    return pages_data

def ocr_image_bytes(image_bytes):
    try:
        img = Image.open(BytesIO(image_bytes)).convert("L")
        img = ImageEnhance.Contrast(img).enhance(2.0)
        img = img.point(lambda x: 0 if x < 180 else 255, '1')
        text = pytesseract.image_to_string(img, config="--psm 6")
        return text.strip()
    except Exception as e:
        print(f"❌ OCR failed: {e}")
        return ""

def ask_gpt_to_summarize_image_context(ocr_text):
    try:
        prompt = f"""
You are given the OCR text extracted from a scanned PDF page containing technical diagrams, charts, or illustrations.

Your task is to interpret the *visual elements* using clues from the OCR text and describe every technical detail.

Instructions:
- For charts or graphs: Explain axis labels, patterns, trends, peaks, dips, legends, and what the data implies.
- For diagrams: Describe structure, shapes, arrows, relationships, and labels in sequence.
- For tables: Summarize purpose, headers, row/column content, and patterns.
- Be detailed, precise, and avoid generic language. Provide *realistic narrative* of what the image shows based on the OCR context.

OCR Text:
{ocr_text}
"""
        response = openai.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=700
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"❌ GPT Error: {e}")
        return None

# --- PDF Saving ---
def save_summary_pdf(file_name, summaries):
    output_filename = f"image_summary_{file_name}"
    pdf = FPDF()
    pdf.set_auto_page_break(auto=True, margin=15)
    pdf.set_font("Arial", size=11)

    def clean(text):
        return text.encode("latin-1", "replace").decode("latin-1")

    for item in summaries:
        pdf.add_page()
        title = f"Page {item['page']} - OCR-Based Summary:\n\n"
        pdf.multi_cell(0, 10, clean(title + item['summary']))

    pdf_output_bytes = pdf.output(dest='S').encode('latin-1')

    # Save to file (optional)
    with open(output_filename, "wb") as f:
        f.write(pdf_output_bytes)

    insert_summary_pdf_to_db(output_filename, pdf_output_bytes)

# --- Main Processing ---
def process_pdf_from_db():
    pdf_entries = fetch_unsummarized_pdfs()
    if not pdf_entries:
        print("✅ No unsummarized image PDFs found.")
        return

    for pdf_id, file_name, pdf_bytes in pdf_entries:
        print(f"\n🔍 Processing: {file_name}")
        pages = extract_pages_with_images(pdf_bytes)
        all_summaries = []

        for page in pages:
            ocr_text = ocr_image_bytes(page["image_bytes"])
            if not ocr_text:
                continue
            summary = ask_gpt_to_summarize_image_context(ocr_text)
            if summary:
                all_summaries.append({
                    "page": page["page_number"],
                    "summary": summary
                })

        if all_summaries:
            save_summary_pdf(file_name, all_summaries)
        else:
            print(f"⚠ No summaries generated for {file_name}")
        
        mark_as_summarized(pdf_id)

# --- Run ---
if __name__ == "__main__":
    process_pdf_from_db()
