import weaviate
import copy

client = weaviate.Client("http://localhost:8080")  # Update if using remote server

OLD_CLASS = "ChunkEmbeddings"         # existing class you're using
NEW_CLASS = "ChunkEmbeddingsV2"       # name of the new class

# Fetch the existing class schema
schema = client.schema.get()
old_class = next(cls for cls in schema["classes"] if cls["class"] == OLD_CLASS)

# Deep copy and rename
new_class = copy.deepcopy(old_class)
new_class["class"] = NEW_CLASS

# Create the new class in Weaviate
client.schema.create_class(new_class)
print(f"✅ Created new class '{NEW_CLASS}' in Weaviate.")
