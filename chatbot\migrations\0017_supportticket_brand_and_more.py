# Generated by Django 5.2.2 on 2025-07-12 08:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot', '0016_remove_openaiusage_openai_usag_request_a1083c_idx_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='supportticket',
            name='brand',
            field=models.CharField(blank=True, choices=[('DALSA', 'DALSA'), ('FLIR', 'FLIR'), ('Basler', 'Basler'), ('Allied Vision', 'Allied Vision'), ('Cognex', 'Cognex'), ('Other', 'Other')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='camera_configuration_tool',
            field=models.CharField(blank=True, choices=[('IP Config Tool', 'IP Config Tool'), ('eBUS Player', 'eBUS Player'), ('Pylon Viewer', 'Pylon Viewer'), ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON>Ex<PERSON>'), ('Other', 'Other')], max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='family_name',
            field=models.CharField(blank=True, choices=[('Genie Nano', 'Genie Nano'), ('Linea', 'Linea'), ('Ace', 'Ace'), ('Dart', 'Dart'), ('Other', 'Other')], max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='generated_prompt',
            field=models.TextField(blank=True, help_text='Generated prompt for GPT', null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='model_number',
            field=models.CharField(blank=True, help_text='e.g., C1280M-25G', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='operating_system_detailed',
            field=models.CharField(blank=True, choices=[('Windows 10', 'Windows 10'), ('Windows 11', 'Windows 11'), ('Linux Ubuntu 20.04', 'Linux Ubuntu 20.04'), ('Linux Ubuntu 22.04', 'Linux Ubuntu 22.04'), ('CentOS', 'CentOS'), ('Other', 'Other')], max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='problem_categories',
            field=models.JSONField(blank=True, default=list, help_text='Selected problem categories'),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='programming_language',
            field=models.CharField(blank=True, choices=[('C#', 'C#'), ('C++', 'C++'), ('Python', 'Python'), ('Java', 'Java'), ('LabVIEW', 'LabVIEW'), ('Other', 'Other')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='prompt_file_path',
            field=models.CharField(blank=True, help_text='Path to external JSON prompt file', max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='sdk_software_used',
            field=models.CharField(blank=True, choices=[('Sapera LT', 'Sapera LT'), ('Spinnaker', 'Spinnaker'), ('Pylon', 'Pylon'), ('VisionPoint', 'VisionPoint'), ('Other', 'Other')], max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='sdk_version',
            field=models.CharField(blank=True, help_text='e.g., v6.10', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='sensor_type',
            field=models.CharField(blank=True, choices=[('Area Scan', 'Area Scan'), ('Line Scan', 'Line Scan')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='serial_number',
            field=models.CharField(blank=True, help_text='e.g., 12345678', max_length=100, null=True),
        ),
        migrations.AddIndex(
            model_name='openaiusage',
            index=models.Index(fields=['request_timestamp'], name='openai_usag_request_a1083c_idx'),
        ),
        migrations.AddIndex(
            model_name='openaiusage',
            index=models.Index(fields=['model_name'], name='openai_usag_model_n_a7669a_idx'),
        ),
        migrations.AddIndex(
            model_name='openaiusage',
            index=models.Index(fields=['api_type'], name='openai_usag_api_typ_85d831_idx'),
        ),
        migrations.AddIndex(
            model_name='openaiusage',
            index=models.Index(fields=['user'], name='openai_usag_user_id_d4137e_idx'),
        ),
    ]
