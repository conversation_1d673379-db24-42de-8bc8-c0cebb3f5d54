import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function ProblemDescriptionPage({ token }) {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const ticketNumber = searchParams.get("ticket");
  const accessToken = token || localStorage.getItem("access");
  
  const [problemDescription, setProblemDescription] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    if (!ticketNumber) {
      navigate("/actions");
    }
  }, [ticketNumber, navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!problemDescription.trim()) {
      setError("Please describe the problem in detail.");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`${BACKEND_URL}/api/update_ticket_description/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          ticket_number: ticketNumber,
          problem_description: problemDescription.trim(),
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Redirect to chatbot with the ticket
        navigate(`/legacy-chat?ticket=${ticketNumber}&mode=new`);
      } else {
        setError(data.message || "Failed to save description. Please try again.");
      }
    } catch (err) {
      console.error("Error saving description:", err);
      setError("Network error. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "600px", 
      margin: "0 auto", 
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        textAlign: "center"
      }}>
        Describe Your Problem
      </h1>
      
      <p style={{
        fontSize: "1.1rem",
        color: "#666",
        marginBottom: "30px",
        textAlign: "center"
      }}>
        Please describe the problem in detail.
      </p>

      {error && (
        <div style={{
          backgroundColor: "#ffebee",
          color: "#c62828",
          padding: "12px",
          borderRadius: "4px",
          marginBottom: "20px",
          border: "1px solid #ef5350"
        }}>
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: "20px" }}>
          <textarea
            value={problemDescription}
            onChange={(e) => {
              setProblemDescription(e.target.value);
              if (error) setError("");
            }}
            placeholder="Please provide a detailed description of the problem you're experiencing. Include any error messages, steps you've taken, and what you expected to happen..."
            required
            style={{
              width: "100%",
              minHeight: "200px",
              padding: "15px",
              border: "2px solid #ddd",
              borderRadius: "8px",
              fontSize: "16px",
              fontFamily: "Arial, sans-serif",
              resize: "vertical",
              lineHeight: "1.5"
            }}
          />
        </div>

        <div style={{ textAlign: "center" }}>
          <button
            type="submit"
            disabled={loading || !problemDescription.trim()}
            style={{
              padding: "15px 40px",
              backgroundColor: loading || !problemDescription.trim() ? "#ccc" : "#4CAF50",
              color: "white",
              border: "none",
              borderRadius: "8px",
              fontSize: "18px",
              fontWeight: "bold",
              cursor: loading || !problemDescription.trim() ? "not-allowed" : "pointer",
              transition: "background-color 0.2s ease"
            }}
          >
            {loading ? "Saving..." : "Continue to Chat"}
          </button>
        </div>
      </form>

      <div style={{ 
        textAlign: "center", 
        marginTop: "20px",
        fontSize: "14px",
        color: "#666"
      }}>
        Ticket #{ticketNumber}
      </div>
    </div>
  );
}
