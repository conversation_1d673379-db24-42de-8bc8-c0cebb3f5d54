# Generated by Django 5.2.2 on 2025-07-24 09:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot', '0019_remove_duplicate_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='supportticket',
            name='product_category',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='product_family',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='product_hierarchy_path',
            field=models.JSONField(blank=True, help_text="Complete hierarchical path: e.g., ['Camera', 'Area Scan', 'Genie Nano', 'GigE']", null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='product_interface',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='supportticket',
            name='product_subcategory',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True),
        ),
    ]
