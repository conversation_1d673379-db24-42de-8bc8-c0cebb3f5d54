#!/usr/bin/env python3
"""
AI Agent Chatbot - Weaviate + Django Version Startup Script
Starts Weaviate (Docker), Django backend, and React frontend
"""

import subprocess
import time
import os
import sys
import webbrowser
from pathlib import Path

def check_port(port):
    """Check if a port is available."""
    import socket
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) != 0

def check_docker():
    """Check if Docker is running."""
    try:
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def start_weaviate():
    """Start Weaviate using Docker Compose."""
    print("🐳 Starting Weaviate with Docker...")
    
    if not check_docker():
        print("❌ Docker is not running. Please start Docker Desktop first.")
        return False
    
    try:
        # Check if Weaviate is already running
        result = subprocess.run(["docker", "ps"], capture_output=True, text=True)
        if "weaviate" in result.stdout:
            print("✅ Weaviate is already running")
            return True
        
        # Start Weaviate with docker-compose
        if Path("docker-compose.yml").exists():
            subprocess.run(["docker-compose", "up", "-d"], check=True)
            print("⏳ Waiting for Weaviate to start...")
            time.sleep(10)
            
            # Verify Weaviate is running
            result = subprocess.run(["docker", "ps"], capture_output=True, text=True)
            if "weaviate" in result.stdout:
                print("✅ Weaviate started successfully")
                return True
            else:
                print("❌ Failed to start Weaviate")
                return False
        else:
            print("❌ docker-compose.yml not found")
            return False
            
    except Exception as e:
        print(f"❌ Error starting Weaviate: {e}")
        return False

def start_django_backend():
    """Start the Django backend server."""
    print("🚀 Starting Django Backend Server...")
    
    try:
        # Check if port 8000 is available
        if not check_port(8000):
            print("⚠️  Port 8000 is already in use. Please stop any existing Django server.")
            return None
        
        # Start Django server
        process = subprocess.Popen(
            [sys.executable, "manage.py", "runserver", "8000"],
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a moment for the server to start
        time.sleep(3)
        
        # Check if the process is still running
        if process.poll() is None:
            print("✅ Django backend started on http://localhost:8000")
            return process
        else:
            print("❌ Failed to start Django backend")
            return None
            
    except Exception as e:
        print(f"❌ Error starting Django backend: {e}")
        return None

def start_react_frontend():
    """Start the React frontend."""
    print("🌐 Starting React Frontend...")
    
    frontend_path = Path("frontend")
    if not frontend_path.exists():
        print("❌ Frontend directory not found")
        return None
    
    try:
        # Check if port 3000 is available
        if not check_port(3000):
            print("⚠️  Port 3000 is already in use. Please stop any existing React server.")
            return None
        
        # Start React development server
        process = subprocess.Popen(
            ["npm", "start"],
            cwd=frontend_path,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for React to start
        print("⏳ Waiting for React to start...")
        time.sleep(15)
        
        # Check if the process is still running
        if process.poll() is None:
            print("✅ React frontend started on http://localhost:3000")
            return process
        else:
            print("❌ Failed to start React frontend")
            return None
            
    except Exception as e:
        print(f"❌ Error starting React frontend: {e}")
        return None

def main():
    """Main function to start the application."""
    print("=" * 70)
    print("🤖 AI Agent Chatbot - Weaviate + Django Version")
    print("=" * 70)
    
    # Start Weaviate first
    if not start_weaviate():
        print("❌ Failed to start Weaviate. Please check Docker.")
        return
    
    # Start Django backend
    backend_process = start_django_backend()
    if not backend_process:
        print("❌ Failed to start Django backend")
        return
    
    # Start React Chat frontend
    print("\n🌐 Starting React Chat Interface...")
    frontend_process = start_react_frontend()
    if not frontend_process:
        print("❌ Failed to start React frontend")
        backend_process.terminate()
        return
    
    print("\n" + "=" * 70)
    print("🎉 AI Agent Chatbot (Weaviate + Django) Started Successfully!")
    print("=" * 70)
    print("🐳 Weaviate Vector DB: http://localhost:8080")
    print("📊 Django Backend: http://localhost:8000")
    print("🌐 React Chat Interface: http://localhost:3000")
    print("=" * 70)
    print("\n💬 Features:")
    print("• Weaviate vector database integration")
    print("• Django REST Framework backend")
    print("• Conversational AI interface")
    print("• Real-time responses with RAG")
    print("• Technical documentation expertise")
    print("\n🔍 Try asking:")
    print("• 'How do I configure the camera?'")
    print("• 'What are the installation steps?'")
    print("• 'Explain the frame rate settings'")
    print("• 'Help with troubleshooting'")
    print("\n⚠️  Press Ctrl+C to stop the application")
    
    # Open browser automatically
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:3000')
        print("🌐 Opened chat interface in your browser")
    except:
        print("💡 Please open http://localhost:3000 in your browser")
    
    try:
        # Keep the script running
        while backend_process.poll() is None and frontend_process.poll() is None:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping application...")
        backend_process.terminate()
        frontend_process.terminate()
        print("✅ Application stopped")
        print("💡 Weaviate will continue running in Docker. Use 'docker-compose down' to stop it.")

if __name__ == "__main__":
    main()
