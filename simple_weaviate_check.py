#!/usr/bin/env python3
import weaviate

client = weaviate.Client("http://localhost:8080")
schema = client.schema.get()

print("🔍 CURRENT WEAVIATE DATABASE STATUS")
print("=" * 50)

for cls in schema.get("classes", []):
    class_name = cls["class"]
    try:
        result = client.query.aggregate(class_name).with_meta_count().do()
        count = result["data"]["Aggregate"][class_name][0]["meta"]["count"]
        print(f"📋 {class_name}: {count} objects")
    except:
        print(f"📋 {class_name}: Error getting count")

print("\n🎯 WHAT THIS MEANS:")
print("- ChunkEmbeddings/ChunkEmbeddingsV2 = OLD format (all files mixed together)")
print("- AreaScan/LineScan/etc = NEW format (files separated by category)")
print("- You currently have OLD format - need to migrate to NEW format")
