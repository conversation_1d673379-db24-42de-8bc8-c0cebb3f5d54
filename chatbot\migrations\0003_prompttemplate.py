# Generated by Django 5.2.2 on 2025-06-19 07:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot', '0002_customuser'),
    ]

    operations = [
        migrations.CreateModel(
            name='PromptTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('chat', 'Chat Query Prompt'), ('img2', 'GPT Vision Prompt')], max_length=100, unique=True)),
                ('description', models.TextField()),
                ('template', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('last_modified', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
