import weaviate

client = weaviate.Client("http://localhost:8080")  # Adjust if using remote Weaviate

client.schema.delete_class("ChunkEmbeddingsV2")
client.schema.delete_class("AreaScanChunks")
client.schema.delete_class("LineScanChunks")
client.schema.delete_class("areascanv2")
client.schema.delete_class("CameraManualV4")
client.schema.delete_class("CameraManualV3")
print("Class 'ChunkEmbeddings' and all data deleted.")

