{"ticket_number": "TCKT-8E4Y902B", "prompt": "[PRODUCT DETAILS]\n- Product: Camera\n- Brand: DALSA\n- Sensor Type: Area Scan\n- Family: Genie Nano\n- Model: M1920\n- Serial Number: SN123456\n- SDK: Sapera LT (v8.70)\n- Programming Language: Python\n- Configuration Tool: CamExpert\n- Operating System: Windows 11\n\n[ISSUE CATEGORY]\n- Category: Detection, Frame Rate Issue\n\n[USER'S DESCRIPTION]\n\"What should I check for detection issues?\" \n\n[CHAT HISTORY]\nFull previous conversation with the user:\n(No previous conversation)\n\n[DOCUMENT CONTEXT]\n(Extracted chunks from uploaded user document or vector DB):\n1. \"Test context chunk about camera detection\"", "last_updated": "2025-07-12T12:10:34.921515+00:00"}