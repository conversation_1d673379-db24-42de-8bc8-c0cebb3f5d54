# Generated by Django 5.2.2 on 2025-07-09 10:04

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot', '0014_supportticket_po_number'),
    ]

    operations = [
        migrations.CreateModel(
            name='OpenAIUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_name', models.CharField(help_text='OpenAI model used (e.g., gpt-4o-mini, text-embedding-ada-002)', max_length=100)),
                ('api_type', models.CharField(choices=[('chat_completion', 'Chat Completion'), ('embedding', 'Embedding'), ('other', 'Other')], default='chat_completion', max_length=50)),
                ('prompt_tokens', models.Integer<PERSON>ield(default=0, help_text='Number of tokens in the prompt')),
                ('completion_tokens', models.IntegerField(default=0, help_text='Number of tokens in the completion')),
                ('total_tokens', models.IntegerField(default=0, help_text='Total tokens used')),
                ('cost_usd', models.DecimalField(decimal_places=6, help_text='Cost in USD', max_digits=10)),
                ('request_timestamp', models.DateTimeField(default=django.utils.timezone.now, help_text='When the API call was made')),
                ('response_time_ms', models.IntegerField(blank=True, help_text='Response time in milliseconds', null=True)),
                ('purpose', models.CharField(blank=True, help_text="Purpose of the API call (e.g., 'chat_response', 'embedding_generation')", max_length=200)),
                ('user', models.ForeignKey(blank=True, help_text='User who made the request', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'openai_usage',
                'ordering': ['-request_timestamp'],
                'indexes': [models.Index(fields=['request_timestamp'], name='openai_usag_request_a1083c_idx'), models.Index(fields=['model_name'], name='openai_usag_model_n_a7669a_idx'), models.Index(fields=['api_type'], name='openai_usag_api_typ_85d831_idx'), models.Index(fields=['user'], name='openai_usag_user_id_d4137e_idx')],
            },
        ),
    ]
