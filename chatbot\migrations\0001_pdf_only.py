from django.db import migrations, models

class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='PdfFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(max_length=255, unique=True)),
                ('file_data', models.BinaryField()),
                ('last_modified', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'pdf_files',
            },
        ),
    ]
