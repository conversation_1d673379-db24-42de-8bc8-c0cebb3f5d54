import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import "./AuthForm.css";

const useQuery = () => new URLSearchParams(useLocation().search);

export default function AuthForm({ onLoginSuccess, defaultMode = "login" }) {
  const query = useQuery();
  const navigate = useNavigate();

  const modeFromQuery = query.get("mode");          // ?mode=signup|login|null
  const initialIsLogin =
    modeFromQuery === "signup" || defaultMode === "signup" ? false : true;

  const [isLogin, setIsLogin] = useState(initialIsLogin);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (modeFromQuery === "signup") setIsLogin(false);
    else if (modeFromQuery === "login") setIsLogin(true);
  }, [modeFromQuery]);

  /* ── form state ───────────────────────────────────────────── */
  const [loginData, setLoginData] = useState({ official_email: "", password: "" });
  const [signupData, setSignupData] = useState({
    state: "",
    name: "",
    address: "",
    organization: "",
    official_email: "",
    alternativeEmail: "",
    phone: "",
    mobile: "",
    password: "",
    confirmPassword: "",
  });

  const handleLoginChange  = e => setLoginData({  ...loginData,  [e.target.name]: e.target.value });
  const handleSignupChange = e => setSignupData({ ...signupData, [e.target.name]: e.target.value });

  const switchToLogin  = () => { setError(""); setSuccess(""); setIsLogin(true);  };
  const switchToSignup = () => { setError(""); setSuccess(""); setIsLogin(false); };

  /* ── login submit ─────────────────────────────────────────── */
  const handleLoginSubmit = async (e) => {
  e.preventDefault();
  if (!loginData.official_email || !loginData.password) {
    setError("Please enter official email and password");
    return;
  }
  setLoading(true);
  setError("");
  setSuccess("");
  try {
    const res = await fetch("http://localhost:8000/api/token/", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(loginData),
    });
    const data = await res.json();
    if (res.ok) {
      // Merge is_admin into user object explicitly:
      const userWithAdminFlag = { ...data.user, is_admin: data.is_admin };

      localStorage.setItem("access", data.access);
      localStorage.setItem("refresh", data.refresh);
      localStorage.setItem("userData", JSON.stringify(userWithAdminFlag));

      setLoginData({ official_email: "", password: "" });

      // Notify parent component (optional, for state)
      onLoginSuccess?.(userWithAdminFlag, { access: data.access, refresh: data.refresh });

      // Navigate based on admin flag
      if (data.is_admin) {
        navigate("/admin");
      } else {
        navigate("/");
      }
    } else {
      setError(data.detail || "Login failed");
    }
  } catch (err) {
    setError("Network error: " + err.message);
  }
  setLoading(false);
};


  /* ── signup submit ────────────────────────────────────────── */
  const handleSignupSubmit = async (e) => {
    e.preventDefault();
    const {
      state, name, address, organization, official_email,
      alternativeEmail, phone, mobile, password, confirmPassword
    } = signupData;

    if (
      !state || !name || !address || !organization || !official_email ||
      !phone || !mobile || !password || !confirmPassword
    ) { setError("Please fill all required fields"); return; }
    if (password !== confirmPassword) {
      setError("Passwords do not match"); return;
    }

    setLoading(true); setError(""); setSuccess("");
    try {
      const res  = await fetch("http://127.0.0.1:8000/api/signup/", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          state, name, address, organization,
          official_email,
          alt_email: alternativeEmail,
          phone, mobile,
          password, password2: confirmPassword,
        }),
      });
      const data = await res.json();
      if (res.ok) {
        setSignupData({
          state:"", name:"", address:"", organization:"",
          official_email:"", alternativeEmail:"", phone:"",
          mobile:"", password:"", confirmPassword:"",
        });
        setSuccess("Signup successful! Please log in below.");
        setIsLogin(true);
      } else setError(data.error || "Signup failed");
    } catch (err) {
      setError("Network error: " + err.message);
    }
    setLoading(false);
  };

  /* ── render ───────────────────────────────────────────────── */
  return (
    <div className="auth-container">
      <div className="auth-card">
        <h2 className="auth-title">{isLogin ? "Login" : "Sign Up"}</h2>

        {error   && <div className="auth-error">{error}</div>}
        {success && <div className="auth-success">{success}</div>}

        <form onSubmit={isLogin ? handleLoginSubmit : handleSignupSubmit}>
          <fieldset disabled={loading} className="auth-fieldset">
            {isLogin ? (
              <>
                <input
                  className="auth-input"
                  type="email"
                  name="official_email"
                  placeholder="Official Email"
                  value={loginData.official_email}
                  onChange={handleLoginChange}
                  required
                />
                <input
                  className="auth-input"
                  type="password"
                  name="password"
                  placeholder="Password"
                  value={loginData.password}
                  onChange={handleLoginChange}
                  required
                />
              </>
            ) : (
              <>
                {[ 
                  { name:"state",            ph:"State" },
                  { name:"name",             ph:"Name" },
                  { name:"address",          ph:"Address" },
                  { name:"organization",     ph:"Organization" },
                  { name:"official_email",   ph:"Official Email", type:"email" },
                  { name:"alternativeEmail", ph:"Alternative Email", type:"email", opt:true },
                  { name:"phone",            ph:"Phone",  type:"tel" },
                  { name:"mobile",           ph:"Mobile", type:"tel" },
                  { name:"password",         ph:"Password", type:"password" },
                  { name:"confirmPassword",  ph:"Confirm Password", type:"password" },
                ].map(({ name, ph, type="text", opt }) => (
                  <input
                    key={name}
                    className="auth-input"
                    type={type}
                    name={name}
                    placeholder={ph}
                    value={signupData[name]}
                    onChange={handleSignupChange}
                    required={!opt}
                  />
                ))}
              </>
            )}

            <button type="submit" className="auth-button" disabled={loading}>
              {loading
                ? isLogin ? "Logging in…" : "Signing up…"
                : isLogin ? "Login"      : "Sign Up"}
            </button>
          </fieldset>
        </form>

        <p className="auth-switch">
          {isLogin ? "New here?" : "Already have an account?"}{" "}
          <button
            type="button"
            className="auth-switch-link"
            onClick={isLogin ? switchToSignup : switchToLogin}
          >
            {isLogin ? "Sign up" : "Login"}
          </button>
        </p>
      </div>
    </div>
  );
}
